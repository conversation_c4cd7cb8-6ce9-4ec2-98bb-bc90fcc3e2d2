<?php

/**
 * Define the internationalization functionality
 *
 * Loads and defines the internationalization files for this plugin
 * so that it is ready for translation.
 *
 * @link       https://wordpress.org/plugins/review-next/
 * @since      1.0.0
 *
 * @package    Review_Plugin
 * @subpackage Review_Plugin/includes
 */

/**
 * Define the internationalization functionality.
 *
 * Loads and defines the internationalization files for this plugin
 * so that it is ready for translation.
 *
 * @since      1.0.0
 * @package    Review_Plugin
 * @subpackage Review_Plugin/includes
 * <AUTHOR> <https://profiles.wordpress.org/nazmulhosen/>
 */
class Review_Plugin_i18n
{


	/**
	 * Load the plugin text domain for translation.
	 *
	 * Note: WordPress automatically loads translations for plugins hosted on WordPress.org
	 * since WordPress 4.6, so this method is no longer needed for WordPress.org plugins.
	 *
	 * @since    1.0.0
	 */
	public function load_plugin_textdomain()
	{
		// WordPress automatically loads translations for WordPress.org plugins since 4.6
		// No need to call load_plugin_textdomain() for plugins hosted on WordPress.org

		// Uncomment the following lines if you need to support WordPress versions older than 4.6
		// or if your plugin is not hosted on WordPress.org:
		/*
		load_plugin_textdomain(
			'review-next-for-woocommerce',
			false,
			dirname(dirname(plugin_basename(__FILE__))) . '/languages/'
		);
		*/
	}
}
