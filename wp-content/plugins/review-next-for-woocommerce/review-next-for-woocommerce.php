<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * admin area. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * that starts the plugin.
 *
 * @link              https://nazmul.xyz
 * @since             1.0
 * @package           Review_Plugin
 *
 * @wordpress-plugin
 * Plugin Name:       Review Next for WooCommerce
 * Plugin URI:        https://wordpress.org/plugins/review-next-woocommerce
 * Description:       Review Next for WooCommerce is a plugin that adds a new tab to your WooCommerce product pages, allowing customers to leave reviews and ratings.
 * Author:            Nazmul Hosen
 * Version:           1.0
 * Requires at least: 5.0
 * Tested up to: 6.8
 * Requires PHP: 7.2
 * Requires Plugins:  woocommerce
 * Author URI:        https://profiles.wordpress.org/nazmul111/
 * License:           GPLv2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       review-next-for-woocommerce
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
/**
 * Currently plugin version.
 * Start at version 1.0 and use SemVer - https://semver.org
 * Rename this for your plugin and update it as you release new versions.
 */
define('REVIEW_PLUGIN_VERSION', '1.0');

/**
 * The code that runs during plugin activation.
 * This action is documented in includes/class-review-plugin-activator.php
 */
function revinefo_activate_review_plugin()
{
    require_once plugin_dir_path(__FILE__) . 'includes/class-review-plugin-activator.php';
    Review_Plugin_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 * This action is documented in includes/class-review-plugin-deactivator.php
 */
function revinefo_deactivate_review_plugin()
{
    require_once plugin_dir_path(__FILE__) . 'includes/class-review-plugin-deactivator.php';
    Review_Plugin_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'revinefo_activate_review_plugin');
register_deactivation_hook(__FILE__, 'revinefo_deactivate_review_plugin');

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require plugin_dir_path(__FILE__) . 'includes/class-review-plugin.php';

/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0
 */
function revinefo_run_review_plugin()
{

    $plugin = new Review_Plugin();
    $plugin->run();
}

revinefo_run_review_plugin();

/**
 * Get attachment ID by file path and filename
 *
 * @param string $file_path Full path to the file
 * @param string $filename The filename to search for
 * @return int|false Attachment ID if found, false otherwise
 */
function review_plugin_get_attachment_id_by_path($file_path, $filename)
{
    // Create a cache key for this lookup
    $cache_key = 'review_plugin_attachment_' . md5($filename);
    $cached_result = wp_cache_get($cache_key, 'review_plugin');

    if (false !== $cached_result) {
        return $cached_result;
    }

    // First, try to get attachment ID by URL
    $file_url = plugins_url('public/images/' . $filename, __FILE__);
    $attachment_id = attachment_url_to_postid($file_url);

    if ($attachment_id) {
        wp_cache_set($cache_key, $attachment_id, 'review_plugin', HOUR_IN_SECONDS);
        return $attachment_id;
    }

    // Try to find attachment by searching post title/name (more efficient than meta_query)
    $filename_without_ext = pathinfo($filename, PATHINFO_FILENAME);

    // Search by post name (slug) first - this is indexed and faster
    $query_args = array(
        'post_type' => 'attachment',
        'post_status' => 'inherit',
        'posts_per_page' => 1,
        'name' => $filename_without_ext,
        'fields' => 'ids'
    );

    $attachments = new WP_Query($query_args);

    if (!empty($attachments->posts)) {
        $attachment_id = $attachments->posts[0];
        wp_cache_set($cache_key, $attachment_id, 'review_plugin', HOUR_IN_SECONDS);
        return $attachment_id;
    }

    // If not found by name, try searching by title
    $query_args = array(
        'post_type' => 'attachment',
        'post_status' => 'inherit',
        'posts_per_page' => 1,
        's' => $filename_without_ext,
        'fields' => 'ids'
    );

    $attachments = new WP_Query($query_args);

    if (!empty($attachments->posts)) {
        $attachment_id = $attachments->posts[0];
        wp_cache_set($cache_key, $attachment_id, 'review_plugin', HOUR_IN_SECONDS);
        return $attachment_id;
    }

    // Cache the negative result to avoid repeated lookups
    wp_cache_set($cache_key, false, 'review_plugin', HOUR_IN_SECONDS);
    return false;
}

// Remove the Default Review Tab
add_filter('woocommerce_product_tabs', 'revinefo_remove_default_review_tab', 98);

function revinefo_remove_default_review_tab($tabs)
{
    unset($tabs['reviews']);
    return $tabs;
}

// Add a custom tab for Reviews & Comments
add_filter('woocommerce_product_tabs', 'revinefo_add_reviews_and_comments_tab');

function revinefo_add_reviews_and_comments_tab($tabs)
{
    // Add a new tab with the title "Reviews & Comments"
    $tabs['reviews_comments_tab'] = array(
        'title' => __('Reviews', 'review-next-for-woocommerce'),
        'priority' => 50,
        'callback' => 'revinefo_reviews_and_comments_tab_content',
        // Callback function to display tab content
    );

    // Get the product ID
    global $product;
    $args = array(
        'post_id' => $product->get_id(),
        'type' => 'review',
        'count' => true // return only the count
    );
    // Get the number of reviews for the product
    $review_count = get_comments($args);

    // Modify the tab title to include the review count
    $tabs['reviews_comments_tab']['title'] = sprintf(
        /* translators: %d: number of reviews */
        __('Reviews (%d)', 'review-next-for-woocommerce'),
        $review_count
    );


    return $tabs;
}


// Callback function to display the content of the "Reviews & Comments" tab
function revinefo_reviews_and_comments_tab_content()
{


    global $product;

    // Get the current user's ID
    $show_review_box_singular = get_option('show_review_box_singular', 'registered_users_and_guests');
    // Get the product ID
    $product_id = $product->get_id();
    // Check if the user has purchased the product
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comment'])) {

        // Verify the nonce
        if (isset($_POST['review_nonce']) && wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['review_nonce'])), 'submit_review_nonce')) {
            // The nonce is valid, proceed with form processing

            // Process the submitted form data
            $rating = isset($_POST['rating']) ? intval($_POST['rating']) : 0;
            $comment = wp_kses_post(wp_unslash($_POST['comment']));

            // Ensure the rating is within a valid range (e.g., 1 to 5)
            if ($rating < 0 || $rating > 5) {
                wp_die(esc_html__('Error: Please select a valid rating between 1 and 5.', 'review-next-for-woocommerce'));
            } elseif (empty($comment)) {
                wp_die(esc_html__('Error: Please add a comment with your rating.', 'review-next-for-woocommerce'));
            } else {
                // Handle image uploads
                $uploaded_images = array();

                if (!empty($_FILES['image_upload'])) {
                    // Include WordPress functions for media handling
                    require_once(ABSPATH . 'wp-admin/includes/image.php');
                    require_once(ABSPATH . 'wp-admin/includes/file.php');
                    require_once(ABSPATH . 'wp-admin/includes/media.php');

                    // Handle the file upload and create attachment
                    // 'image_upload' is the name of the file input field
                    // $product_id is the post ID to attach the image to (the product)
                    $attachment_id = media_handle_upload('image_upload', $product_id);

                    // Check for errors
                    if (is_wp_error($attachment_id)) {
                        // Handle the error gracefully without exposing details
                        wp_die(esc_html__('Error uploading image. Please try again.', 'review-next-for-woocommerce'));
                    } else {
                        // Save the attachment ID as comment meta
                        if (!is_wp_error($comment_id)) {
                            update_comment_meta($comment_id, 'review_image_id', $attachment_id);
                            // Optionally, delete the old review_image_url meta if it exists
                            // delete_comment_meta($comment_id, 'review_image_url');
                        } else {
                            // Log error only in debug mode
                            if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                                // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
                                error_log('Error updating comment meta with attachment ID for comment: ' . $comment_id);
                            }
                        }
                    }
                }


                // Create a new comment
                $current_user_id = get_current_user_id();
                $current_user = get_user_by('ID', $current_user_id);
                $display_name = !isset($current_user->display_name) ? '' : $current_user->display_name;
                $user_email = !isset($current_user->user_email) ? '' : $current_user->user_email;
                $author_url = !isset($current_user->user_url) ? '' : $current_user->user_url;
                $comment_data = array(
                    'comment_post_ID' => $product_id,
                    'comment_author' => $display_name,
                    'comment_author_email' => $user_email,
                    'comment_author_url' => $author_url,
                    'comment_content' => $comment,
                    'comment_type' => 'review',
                    'comment_meta' => array(
                        'rating' => $rating,
                        // Save the rating as comment metadata
                    ),
                    'user_id' => $current_user_id,
                );

                add_filter('duplicate_comment_id', '__return_false');

                $comment_id = wp_new_comment($comment_data);

                if ($comment_id) {
                    // Attach uploaded images to the comment
                    if (!empty($uploaded_images)) {
                        // Include WordPress functions for media handling
                        require_once(ABSPATH . 'wp-admin/includes/image.php');
                        require_once(ABSPATH . 'wp-admin/includes/file.php');
                        require_once(ABSPATH . 'wp-admin/includes/media.php');
                        // Attach uploaded images to the comment
                        foreach ($uploaded_images as $image_path) {
                            // Check if the file exists
                            if (file_exists($image_path)) {

                                $image_url = str_replace(ABSPATH, site_url('/'), $image_path);

                                // Associate the comment with the uploaded image
                                if (!is_wp_error($comment_id)) {
                                    update_comment_meta($comment_id, 'review_image_url', $image_url);
                                } else {
                                    // Handle attachment insertion error
                                    if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                                        // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
                                        error_log('Error inserting attachment: ' . $attachment_id->get_error_message());
                                    }
                                }
                            } else {
                                // Handle the case where the file doesn't exist
                                if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                                    // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
                                    error_log('File does not exist: ' . $image_path);
                                }
                            }
                        }
                    }
                    include(plugin_dir_path(__FILE__) . 'template/frontend/review-success-message.php');
                }
            }
            // After processing, redirect to the same page with a success parameter
            // wp_redirect(add_query_arg('success', 'true'));
            // exit;
            // ob_end_flush();
        } else {
            wp_die('Security check failed. Please try again.');
        }
    }

    if ($show_review_box_singular == 1) {
        // Show the review boxes
        include(plugin_dir_path(__FILE__) . 'template/frontend/review-ratting-form.php');
    }

    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reply_comment'])) {
        // Check if the parent comment ID and post ID are set
        if (isset($_POST['comment_parent']) && isset($_POST['comment_post_ID'])) {
            // Create a new comment reply
            $current_user_id = get_current_user_id();
            $current_user = get_user_by('ID', $current_user_id);
            $display_name = !isset($current_user->display_name) ? '' : $current_user->display_name;
            $user_email = !isset($current_user->user_email) ? '' : $current_user->user_email;
            $author_url = !isset($current_user->user_url) ? '' : $current_user->user_url;
            // Sanitize and validate data as needed
            $comment_content = isset($_POST['comment_content']) ? wp_kses_post(wp_unslash($_POST['comment_content'])) : '';
            $comment_post_ID = isset($_POST['comment_post_ID']) ? intval($_POST['comment_post_ID']) : 0;
            $comment_parent = isset($_POST['comment_parent']) ? intval($_POST['comment_parent']) : 0;

            $comment_data = array(
                'comment_content' => $comment_content,
                'comment_post_ID' => $comment_post_ID,
                'comment_parent' => $comment_parent,
                'comment_author' => $display_name,
                'comment_author_email' => $user_email,
                'comment_author_url' => $author_url,
                'comment_type' => 'review_reply',
                'user_id' => $current_user_id,
            );

            // Insert the reply comment
            $comment_id = wp_insert_comment($comment_data);
        }
    }


    // }

    if ($show_review_box_singular == 1) {
        // Display existing average ratings
        include(plugin_dir_path(__FILE__) . 'template/frontend/average-rating.php');

        // Display existing reviews with uploaded images and videos
        include(plugin_dir_path(__FILE__) . 'template/frontend/product-review-show.php');
    }
}


// Define the AJAX action for logged-in users
add_action('wp_ajax_filter_comments', 'revinefo_filter_comments');

// Define the AJAX action for non-logged-in users
add_action('wp_ajax_nopriv_filter_comments', 'revinefo_filter_comments');

function revinefo_filter_comments()
{
    // Verify nonce
    if (!isset($_POST['filter_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['filter_nonce'])), 'filter_comments_nonce')) {
        wp_die('Invalid nonce');
    }

    // Get the selected filters from the AJAX request
    $product_id = isset($_POST['product_id']) ? intval(wp_unslash($_POST['product_id'])) : '';
    $selectedRating = isset($_POST['selected_rating']) ? intval(wp_unslash($_POST['selected_rating'])) : 0;
    $sortOption = isset($_POST['sort_option']) ? sanitize_text_field(wp_unslash($_POST['sort_option'])) : 'recent';
    $isVerified = isset($_POST['is_verified']) ? intval(wp_unslash($_POST['is_verified'])) : 0;
    $hasImagesVideos = isset($_POST['has_images_videos']) ? intval(wp_unslash($_POST['has_images_videos'])) : 0;
    // Define query arguments to filter comments
    $args = array(
        'post_id' => $product_id,
        'status' => 'approve',
        'number' => 15,
        'order' => 'DESC',
        'type' => 'review',
        'parent' => 0,
    );

    // Only add meta_query if filters are actually being used
    $meta_query = array();
    $has_meta_filters = false;

    if ($selectedRating > 0) {
        // Add meta query for rating
        $meta_query[] = array(
            'key' => 'rating',
            'value' => $selectedRating,
            'compare' => '=',
            'type' => 'NUMERIC',
        );
        $has_meta_filters = true;
    }

    if ($isVerified) {
        // Add meta query for verified owner
        $meta_query[] = array(
            'key' => 'verified_owner',
            'value' => 'yes',
            'compare' => '=',
        );
        $has_meta_filters = true;
    }

    if ($hasImagesVideos) {
        // Add meta query for reviews with images/videos
        $meta_query[] = array(
            'key' => 'has_images',
            'value' => 'yes',
            'compare' => '=',
        );
        $has_meta_filters = true;
    }

    // Only add meta_query to args if we have meta filters
    if ($has_meta_filters) {
        // phpcs:ignore WordPress.DB.SlowDBQuery.slow_db_query_meta_query -- Optimized: only used when filters are applied
        $args['meta_query'] = $meta_query;
        if (count($meta_query) > 1) {
            $args['meta_query']['relation'] = 'AND';
        }
    }
    // // Get comments based on the arguments
    $comments = get_comments($args);
    // Sort the comments based on the selected sorting option
    if ($sortOption === 'high_to_low') {
        // Sort by high-to-low rating
        usort($comments, function ($a, $b) {
            $ratingA = get_comment_meta($a->comment_ID, 'rating', true);
            $ratingB = get_comment_meta($b->comment_ID, 'rating', true);
            return $ratingB - $ratingA;
        });
    } elseif ($sortOption === 'low_to_high') {
        // Sort by low-to-high rating
        usort($comments, function ($a, $b) {
            $ratingA = get_comment_meta($a->comment_ID, 'rating', true);
            $ratingB = get_comment_meta($b->comment_ID, 'rating', true);
            return $ratingA - $ratingB;
        });
    }
    // Default is 'star'
    $rating_images = get_option('rating_images', 'star');

    // Define an array with the icon classes for different options
    $icon_classes = array(
        'star' => array(
            'filled' => 'fas fa-star icon_filled',
            'empty' => 'far fa-star icon_empty',
        ),
        'heart' => array(
            'filled' => 'fas fa-heart icon_filled',
            'empty' => 'far fa-heart icon_empty',
        ),
        'thumb' => array(
            'filled' => 'fas fa-thumbs-up icon_filled',
            'empty' => 'far fa-thumbs-up icon_empty',
        ),
    );


    if (count($comments) > 0) {
        foreach ($comments as $comment) {
            // Assuming the comment contains a "rating" value (e.g., from 1 to 5)
            $rating = get_comment_meta($comment->comment_ID, 'rating', true);
            // Retrieve the customer's name, image URL, and date from comment metadata or custom fields
            $authorUsername = get_comment_author($comment->comment_ID);
            $commentDate = gmdate('F j, Y', strtotime($comment->comment_date));
            // Retrieve the user's avatar URL or use a default image URL
            $avatarURL = get_avatar_url($comment, array('size' => 192));
            $vote_count = get_comment_meta($comment->comment_ID, 'vote_count', true);
            $currentUserId = get_current_user_id();
            $currentUserVote = get_comment_meta($comment->comment_ID, 'user_vote_' . $currentUserId, true);
            // var_dump($vote_count, $currentUserId, $currentUserVote);
            // Check if a custom avatar exists; if not, use a default image URL
            if (empty($avatarURL)) {
                $image_url = plugins_url('public/images/', __FILE__);
                // Append the specific image filename to the URL
                $image_filename = 'review-plugin-user.jpg';
                $avatarURL = $image_url . $image_filename;
            }
            echo '<div class="review-item">';
            echo '<div class="cards">';
            echo '<div class="user-info comment-user">';
            if (is_numeric($avatarURL)) {
                echo wp_get_attachment_image($avatarURL, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'user-img'));
            } else {
                // Use wp_get_attachment_image for better handling
                $avatar_id = attachment_url_to_postid($avatarURL);
                if ($avatar_id) {
                    echo wp_get_attachment_image($avatar_id, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'user-img'));
                } else {
                    // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for non-attachment images
                    echo '<img class="user_img" src="' . esc_url($avatarURL) . '" alt="user-img">';
                }
            }
            echo '<div>';
            echo '<p class="user-name">' . esc_html($authorUsername) . '</p>';
            echo '<p class="verified">Verified</p>';
            echo '<p class="create_date">' . esc_html($commentDate) . '</p>';
            echo '</div>';
            echo '</div>';
            // Display star rating based on the "rating" value
            echo '<div class="rating_start">';
            if ($rating > 0) {
                for ($i = 1; $i <= 5; $i++) {
                    $iconType = ($i <= $rating) ? 'filled' : 'empty';
                    $starClass = $icon_classes[$rating_images][$iconType];
                    echo '<i class="' . esc_attr($starClass) . '"></i>';
                }
            }

            echo '<div class="vote_sec" data-comment-id="' . esc_attr($comment->comment_ID) . '">';
            echo '<div class="icon vote-buttons">';
            echo '<i class="far fa-thumbs-up thumb_up ' . ($currentUserVote === 'like' ? 'active' : '') . '"  id="thumb_up' . esc_attr($comment->comment_ID) . '" target="' . esc_attr($comment->comment_ID) . '"></i>';
            echo '<span id="vote_count' . esc_attr($comment->comment_ID) . '">' . esc_html($vote_count) . '</span>';
            echo '<i class="far fa-thumbs-down thumb_down ' . ($currentUserVote === 'dislike' ? 'active' : '') . '" id="thumb_down' . esc_attr($comment->comment_ID) . '" target="' . esc_attr($comment->comment_ID) . '"></i>';
            echo '<i class="fas fa-ellipsis-v open_show_hide" id="open_show_hide" target="' . esc_attr($comment->comment_ID) . '"></i>';
            echo '</div>';
            echo '<div class="reply_dropdown"  id="reply_dropdown' . esc_attr($comment->comment_ID) . '" style="display: none;">';
            echo '<h5>Not Helpful</h5>';
            echo '<h5>Report Abuse</h5>';
            echo '<h5 class="comment-reply-trigger" target="' . esc_attr($comment->comment_ID) . '">Comment Reply</h5>';
            echo '</div>';
            echo '</div>';
            echo '</div>';

            // Display comment content
            echo '<div class="review-content">';
            echo '<p>' . esc_html($comment->comment_content) . '</p>';

            // Display uploaded images for this comment
            $comment_image_id = get_comment_meta($comment->comment_ID, 'review_image_id', true);
            $comment_image_url = get_comment_meta($comment->comment_ID, 'review_image_url', true);

            if (!empty($comment_image_id)) {
                $image_link = wp_get_attachment_url($comment_image_id);
                echo '<a href="' . esc_url($image_link) . '" data-fancybox>';
                echo wp_get_attachment_image($comment_image_id, 'medium', false, array(
                    'class' => 'review_img clickable-image',
                    'alt' => esc_attr__('Comment-Image', 'review-next-for-woocommerce')
                ));
                echo '</a>';
            } elseif (!empty($comment_image_url)) {
                // Fallback for legacy images stored as URLs
                echo '<a href="' . esc_url($comment_image_url) . '" data-fancybox >';
                $image_id = attachment_url_to_postid($comment_image_url);
                if ($image_id) {
                    echo wp_get_attachment_image($image_id, 'medium', false, array(
                        'class' => 'review_img clickable-image',
                        'alt' => esc_attr__('Comment-Image', 'review-next-for-woocommerce')
                    ));
                } else {
                    // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for legacy image URLs
                    echo '<img class="review_img clickable-image" src="' . esc_url($comment_image_url) . '" alt="' . esc_attr__('Comment-Image', 'review-next-for-woocommerce') . '">';
                }
                echo '</a>';
            }


            echo '<form action="" method="post" class="reply-comment-form" id="reply-comment-form' . esc_attr($comment->comment_ID) . '" style="display: none;">';
            echo '<textarea class="reply_field" name="comment_content" placeholder="Your Reply" cols="30" rows="15" required></textarea><br>';
            echo '<input type="hidden" name="comment_parent" value="' . esc_attr($comment->comment_ID) . '">';
            echo '<input type="hidden" name="comment_post_ID" value="' . esc_attr($product_id) . '" id="comment_post_ID">';
            echo '<input type="hidden" name="reply_comment" value="1">';
            echo '<input type="hidden" name="_wp_unfiltered_html_comment" id="_wp_unfiltered_html_comment" value="2cda9e4ffe">';
            echo '<input type="submit" value="Submit Reply" class="submit_btn">';
            echo '</form>';
            echo '</div>';
            echo '</div>'; //card

            // Get and output the replies to this comment
            $args = array(
                'parent' => $comment->comment_ID,
                'status' => 'approve',
                'order' => 'ASC',
                'type' => 'review_reply',
            );
            $replies = get_comments($args);
            foreach ($replies as $reply) {
                // Retrieve the customer's name, image URL, and date from comment metadata or custom fields
                $replyauthorUsername = get_comment_author($reply->comment_ID);
                $customerImageURL = get_comment_meta($reply->comment_ID, 'customer_image_url', true);
                $commentDate = gmdate('F j, Y', strtotime($reply->comment_date)); // Format the comment date
                // Retrieve the user's avatar URL or use a default image URL
                $avatarURL = get_avatar_url($reply, array('size' => 192));
                $reply_vote_count = get_comment_meta($reply->comment_ID, 'vote_count', true);
                // Check if a custom avatar exists; if not, use a default image URL
                if (empty($avatarURL)) {
                    $image_url = plugins_url('public/images/', __FILE__);
                    // Append the specific image filename to the URL
                    $image_filename = 'review-plugin-user.jpg';
                    $avatarURL = $image_url . $image_filename;
                }
                echo '<div class="review-reply-card">';
                echo '<div class="user-info comment-user">';
                if (is_numeric($avatarURL)) {
                    echo wp_get_attachment_image($avatarURL, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'user-img'));
                } else {
                    // Use wp_get_attachment_image for better handling
                    $avatar_id = attachment_url_to_postid($avatarURL);
                    if ($avatar_id) {
                        echo wp_get_attachment_image($avatar_id, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'user-img'));
                    } else {
                        // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for non-attachment images
                        echo '<img class="user_img" src="' . esc_url($avatarURL) . '" alt="user-img">';
                    }
                }
                echo '<div>';
                echo '<p class="user-name">' . esc_html($replyauthorUsername) . '</p>';
                echo '<p class="verified">Verified</p>';
                echo '<p class="create_date">' . esc_html($commentDate) . '</p>';
                echo '</div>';
                echo '</div>';
                // Display star rating based on the "rating" value
                echo '<div class="rating_start">';
                echo '<div class="vote_sec" data-comment-id="' . esc_attr($reply->comment_ID) . '">';
                echo '<div class="icon vote-buttons">';
                echo '<i class="far fa-thumbs-up thumb_up ' . ($currentUserVote === 'like' ? 'active' : '') . '"  id="thumb_up' . esc_attr($reply->comment_ID) . '" target="' . esc_attr($reply->comment_ID) . '"></i>';
                echo '<span id="vote_count' . esc_attr($reply->comment_ID) . '">' . esc_html($vote_count) . '</span>';
                echo '<i class="far fa-thumbs-down thumb_down ' . ($currentUserVote === 'dislike' ? 'active' : '') . '" id="thumb_down' . esc_attr($reply->comment_ID) . '" target="' . esc_attr($reply->comment_ID) . '"></i>';
                echo '<i class="fas fa-ellipsis-v open_show_hide" id="open_show_hide" target="' . esc_attr($reply->comment_ID) . '"></i>';
                echo '</div>';
                echo '<div class="reply_dropdown" id="reply_dropdown' . esc_attr($reply->comment_ID) . '" style="display: none;">';
                echo ' <h5>Not Helpful</h5>';
                echo '<h5>Report Abuse</h5>';
                echo '<h5 class="comment-reply-trigger" target="' . esc_attr($reply->comment_ID) . '">Comment Reply</h5>';
                echo '</div>';
                echo '</div>';
                echo '</div>';

                // Display comment content
                echo '<div class="review-content">';
                echo '<p>' . esc_html($reply->comment_content) . '</p>';

                echo '<form action="" method="post" class="reply-comment-form" id="reply-comment-form' . esc_attr($reply->comment_ID) . '" style="display: none;">';
                echo '<textarea class="reply_field" name="comment_content" placeholder="Your Reply" cols="30" rows="15" required></textarea><br>';
                echo '<input type="hidden" name="comment_parent" value="' . esc_attr($comment->comment_ID) . '">';
                echo '<input type="hidden" name="comment_post_ID" value="' . esc_attr($product_id) . '" id="comment_post_ID">';
                echo '<input type="hidden" name="reply_comment" value="1">';
                echo '<input type="hidden" name="_wp_unfiltered_html_comment" id="_wp_unfiltered_html_comment" value="2cda9e4ffe">';
                echo '<input type="submit" value="Submit Reply" class="submit_btn">';
                echo '</form>';
                echo '</div>';
                echo '</div>'; //card

            }
            echo '</div>'; // Close review-item
        }
        // Display comment pagination links
        // echo '<div class="comment-pagination">';
        // paginate_comments_links();
        // echo '</div>';
    } else {
        echo '<p class="not_found"> No comments found.</p>';
    }

    die();
}


// Define AJAX actions for comment voting
add_action('wp_ajax_record_comment_vote', 'revinefo_record_comment_vote'); // For logged-in users
add_action('wp_ajax_nopriv_record_comment_vote', 'revinefo_record_comment_vote'); // For non-logged-in users

function revinefo_record_comment_vote()
{
    // Verify nonce and check request method
    if (!isset($_POST['vote_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['vote_nonce'])), 'comment_vote_nonce')) {
        wp_send_json_error('Nonce verification failed');
        return;
    }

    if (!isset($_SERVER['REQUEST_METHOD']) || $_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['comment_id']) || !isset($_POST['vote_type'])) {
        wp_send_json_error('Invalid request');
        return;
    }

    // Check if user is logged in
    $currentUserId = get_current_user_id();
    if (!$currentUserId) {
        wp_send_json_error('User must be logged in to vote');
        return;
    }

    // Sanitize and validate data
    $commentId = isset($_POST['comment_id']) ? intval(wp_unslash($_POST['comment_id'])) : 0;
    $voteType = isset($_POST['vote_type']) ? sanitize_text_field(wp_unslash($_POST['vote_type'])) : '';

    if (!$commentId || !in_array($voteType, ['like', 'dislike'])) {
        wp_send_json_error('Invalid vote data');
        return;
    }

    // Initialize vote count
    $voteCount = (int)get_comment_meta($commentId, 'vote_count', true);
    $voteCount = $voteCount ?: 0;

    // Get current user's vote
    $currentUserVote = get_comment_meta($commentId, 'user_vote_' . $currentUserId, true);

    // Handle voting logic
    if ($currentUserVote) {
        if ($currentUserVote === $voteType) {
            // Remove vote if clicking same button
            delete_comment_meta($commentId, 'user_vote_' . $currentUserId);
            if ($voteType === 'like') {
                $voteCount--;
            }
            $status = 'removed';
        } else {
            // Change vote type
            update_comment_meta($commentId, 'user_vote_' . $currentUserId, $voteType);
            if ($currentUserVote === 'like' && $voteType === 'dislike') {
                $voteCount--;
            } elseif ($currentUserVote === 'dislike' && $voteType === 'like') {
                $voteCount++;
            }
            $status = 'changed';
        }
    } else {
        // New vote
        add_comment_meta($commentId, 'user_vote_' . $currentUserId, $voteType);
        if ($voteType === 'like') {
            $voteCount++;
        }
        $status = 'added';
    }

    // Update vote count and send response
    update_comment_meta($commentId, 'vote_count', $voteCount);
    wp_send_json_success(array(
        'vote_count' => $voteCount,
        'comment_id' => $commentId,
        'status' => $status
    ));
}


function revinefo_my_account_orders_waiting_review()
{
    include(plugin_dir_path(__FILE__) . 'template/waiting-for-review.php');
}
add_action('woocommerce_before_account_orders', 'revinefo_my_account_orders_waiting_review');


// question tab
function revinefo_add_question_tab($tabs)
{
    $tabs['question_tab'] = array(
        'title'     => __('Questions', 'review-next-for-woocommerce'),
        'priority'  => 50,
        'callback'  => 'revinefo_question_tab_content',
    );
    // Get the product ID
    global $product;
    $args = array(
        'post_id' => $product->get_id(),
        'type' => 'question',
        'status' => 'approve',
        'count' => true // return only the count
    );
    // Get the number of reviews for the product
    $question_count = get_comments($args);

    // Modify the tab title to include the review count

    $tabs['question_tab']['title'] = sprintf(
        /* translators: %d: number of questions */
        __('Questions (%d)', 'review-next-for-woocommerce'),
        $question_count
    );

    return $tabs;
}
add_filter('woocommerce_product_tabs', 'revinefo_add_question_tab');

function revinefo_question_tab_content()
{
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['question'])) {
        // Verify nonce
        if (!isset($_POST['question_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['question_nonce'])), 'submit_question_nonce')) {
            wp_die('Security check failed');
            return;
        }

        // Validate and sanitize question content
        $question = isset($_POST['question']) ? wp_kses_post(wp_unslash($_POST['question'])) : '';
        if (empty($question)) {
            wp_die('Question content is required');
            return;
        }

        $current_user_id = get_current_user_id();
        if (!$current_user_id) {
            wp_die('You must be logged in to submit a question');
            return;
        } // Create a new comment reply
        $question = sanitize_text_field(wp_unslash($_POST['question']));
        $current_user_id = get_current_user_id();
        $current_user = get_user_by('ID', $current_user_id);
        $display_name = !isset($current_user->display_name) ? '' : $current_user->display_name;
        $user_email = !isset($current_user->user_email) ? '' : $current_user->user_email;
        $author_url = !isset($current_user->user_url) ? '' : $current_user->user_url;
        $valid = revinefo_validate_question($question);
        if ($valid) {
            // Sanitize and validate data as needed
            $comment_post_ID = isset($_POST['comment_post_ID']) ? intval($_POST['comment_post_ID']) : 0;
            $comment_parent = isset($_POST['comment_parent']) ? intval($_POST['comment_parent']) : 0;

            $comment_data = array(
                'comment_content' => $question,
                'comment_post_ID' => $comment_post_ID,
                'comment_parent' => $comment_parent,
                'comment_author' => $display_name,
                'comment_author_email' => $user_email,
                'comment_author_url' => $author_url,
                'comment_type' => 'question',
                'user_id' => $current_user_id,
            );

            // Insert the reply comment
            wp_insert_comment($comment_data);

            echo '<p style="color:#F85606">Your question has been submitted successfully.</p>';
            wp_redirect(esc_url(get_permalink()));
        } else {
            echo '<p style="color: #F85606;">Your question should not contain contact information.</p>';
        }
    }
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['question_reply'])) {
        // Verify nonce
        if (!isset($_POST['question_reply_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['question_reply_nonce'])), 'submit_question_reply_nonce')) {
            wp_die('Security check failed');
        }

        // Create a new comment reply
        $question = sanitize_text_field(wp_unslash($_POST['question_reply']));
        $current_user_id = get_current_user_id();
        $current_user = get_user_by('ID', $current_user_id);
        $display_name = !isset($current_user->display_name) ? '' : $current_user->display_name;
        $user_email = !isset($current_user->user_email) ? '' : $current_user->user_email;
        $author_url = !isset($current_user->user_url) ? '' : $current_user->user_url;
        $valid = revinefo_validate_question($question);
        if ($valid) {
            // Sanitize and validate data as needed
            $comment_post_ID = isset($_POST['comment_post_ID']) ? intval($_POST['comment_post_ID']) : 0;
            $comment_parent = isset($_POST['comment_parent']) ? intval($_POST['comment_parent']) : 0;

            $comment_data = array(
                'comment_content' => $question,
                'comment_post_ID' => $comment_post_ID,
                'comment_parent' => $comment_parent,
                'comment_author' => $display_name,
                'comment_author_email' => $user_email,
                'comment_author_url' => $author_url,
                'comment_type' => 'question_reply',
                'user_id' => $current_user_id,
            );

            // Insert the reply comment
            wp_insert_comment($comment_data);

            echo '<p style="color:#F85606">Your question reply has been submitted successfully.</p>';
        } else {
            echo '<p style="color: #F85606;">Your question reply should not contain contact information.</p>';
        }
    }
    $show_question_box_singular = get_option('show_question_box_singular', '1');
    if ($show_question_box_singular == 1) {
        include(plugin_dir_path(__FILE__) . 'template/frontend/user-question.php');
    }
}

function revinefo_validate_question($question)
{
    // Define regular expressions to match email, phone, and web links
    $emailRegex = '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}/';
    $phoneRegex = '/\b\d{10,11}\b/';
    $linkRegex = '/https?:\/\/[^\s\/$.?#]+[^\s]*/';

    // Check if the question matches any of the prohibited patterns
    if (preg_match($emailRegex, $question) || preg_match($phoneRegex, $question) || preg_match($linkRegex, $question)) {
        return false; // Question contains prohibited content
    }

    return true; // Question is valid
}




// --------------------------- admin dashboard setting start -----------------------
function revinefo_custom_admin_menu()
{
    // Create the main "Review" menu
    add_menu_page(
        'Review',
        'Review',
        'manage_options', // Capability required to access
        'review-settings', // Menu slug
        'revinefo_review_settings_page', // Callback function to display content
        'dashicons-star-filled' // Icon (change to your preferred icon)
    );
    add_submenu_page(
        'review-settings',
        'Question',
        'Question',
        'manage_options',
        'review-question',
        'revinefo_question_setting_page'
    );
    // Create submenu pages
    add_submenu_page(
        'review-settings', // Parent menu slug
        'General Setting', // Page title
        'General Setting', // Menu title
        'manage_options',
        'review-general-setting', // Submenu slug
        'revinefo_general_setting_page' // Callback function to display content
    );

    add_submenu_page(
        'review-settings',
        'Styling',
        'Styling',
        'manage_options',
        'review-styling-setting',
        'revinefo_styling_setting_page'
    );

    add_submenu_page(
        'review-settings',
        'Typography',
        'Typography',
        'manage_options',
        'review-typography-setting',
        'revinefo_typography_setting_page'
    );

    add_submenu_page(
        'review-settings',
        'Advanced Setting',
        'Advanced Setting',
        'manage_options',
        'review-advanced-setting',
        'revinefo_advanced_setting_page'
    );
    // Add a submenu item under 'review-settings'
    add_submenu_page(
        'review-settings',
        'Order Details',
        'Order Details',
        'manage_options',
        'order-details',
        'revinefo_custom_order_details_callback'
    );
}
// Hook the menu creation function
add_action('admin_menu', 'revinefo_custom_admin_menu');
// Callback functions to display content for each page
function revinefo_review_settings_page()
{
    echo '<div class="wrap"><h2>review_settings_page</h2>';
    echo '<p>This is the General Setting submenu.</p>';
    // You can add your HTML content here
    echo '</div>';
}

function revinefo_custom_order_details_callback()
{
    include_once(plugin_dir_path(__FILE__) . 'template/admin/order-count-details.php');
}

function revinefo_question_setting_page()
{
    include(plugin_dir_path(__FILE__) . 'template/admin/all-question.php');
}

function revinefo_general_setting_page()
{
?>
<div class="wrap">
    <form method="post" action="options.php">
        <?php
            settings_fields('general_settings_group');
            do_settings_sections('general-settings');
            submit_button();
            ?>
    </form>
</div>
<?php
}
// Callback for the "Show Review Box on Singular Pages" field
function show_review_box_singular_callback()
{
    $show_review_box_singular = get_option('show_review_box_singular', '1');
?>
<input type="checkbox" name="show_review_box_singular" id="show_review_box_singular" value="1"
    <?php checked($show_review_box_singular, 1); ?> />
<label for="show_review_box_singular">Show review boxes in singular pages only</label>
<?php
}


// Callback for the "Who is allowed to rate" field
function allowed_to_rate_callback()
{
    $show_review_box_singular = get_option('allowed_to_rate', 'registered_users_and_guests');
    $choices = array(
        'no_one' => 'No one',
        'registered_users_and_guests' => 'Registered users and guests',
        'completed_orders' => 'Completed Orders',
    );
?>
<?php foreach ($choices as $value => $label) { ?>
<input type="radio" id="allowed_to_rate<?php echo esc_attr($value); ?>" name="allowed_to_rate"
    value="<?php echo esc_attr($value); ?>" <?php checked($show_review_box_singular, $value); ?> />
<label for="allowed_to_rate<?php echo esc_attr($value); ?>"><?php echo esc_html($label); ?></label><br>
<?php } ?>
<?php
}

// Callback for the "Rating Images" field
function rating_images_callback()
{
    $rating_images = get_option('rating_images', 'star'); // Default is 'star'
    $icon_choices = array(
        'star'  => '<span class="dashicons dashicons-star-filled" style="color: gold;"></span> Star',
        'heart' => '<span class="dashicons dashicons-heart" style="color: red;"></span> Heart',
        'thumb' => '<span class="dashicons dashicons-thumbs-up" style="color: blue;"></span> Thumbs-up',
    );
?>
<?php foreach ($icon_choices as $value => $label) { ?>
<input type="radio" id="rating_images_<?php echo esc_attr($value); ?>" name="rating_images"
    value="<?php echo esc_attr($value); ?>" <?php checked($rating_images, $value); ?> />
<label for="rating_images_<?php echo esc_attr($value); ?>"><?php echo wp_kses_post($label); ?></label><br>
<?php } ?>
<?php
}



function review_images_video_upload_option_show_callback()
{
    $review_images_video_upload = get_option('review_images_video_upload', '1');
?>
<input type="checkbox" name="review_images_video_upload" id="review_images_video_upload" value="1"
    <?php checked($review_images_video_upload, 1); ?> />
<label for="review_images_video_upload">Review images video upload option show</label>
<?php
}
function show_question_box_singular_callback()
{
    $show_question_box_singular = get_option('show_question_box_singular', '1');
?>
<input type="checkbox" name="show_question_box_singular" id="show_question_box_singular" value="1"
    <?php checked($show_question_box_singular, 1); ?> />
<label for="show_question_box_singular">Show Question Box on Singular</label>
<?php
}

// Step 4: Register the settings and sections
function general_settings_fields()
{
    add_settings_section('general_settings_section', 'General Settings', 'general_settings_section_callback', 'general-settings');
    add_settings_field('show_review_box_singular', 'Show Review Box on Singular Pages', 'show_review_box_singular_callback', 'general-settings', 'general_settings_section');
    add_settings_field('allowed_to_rate', 'Who is allowed to rate', 'allowed_to_rate_callback', 'general-settings', 'general_settings_section');
    add_settings_field('rating_images', 'Rating Images', 'rating_images_callback', 'general-settings', 'general_settings_section');
    add_settings_field('review_images_video_upload', 'Review images video upload option show', 'review_images_video_upload_option_show_callback', 'general-settings', 'general_settings_section');
    add_settings_field('show_question_box_singular', 'Show Question Box on Singular', 'show_question_box_singular_callback', 'general-settings', 'general_settings_section');
    register_setting('general_settings_group', 'show_review_box_singular', array(
        'sanitize_callback' => 'absint'
    ));
    register_setting('general_settings_group', 'allowed_to_rate', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));
    register_setting('general_settings_group', 'rating_images', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));
    register_setting('general_settings_group', 'review_images_video_upload', array(
        'sanitize_callback' => 'absint'
    ));
    register_setting('general_settings_group', 'show_question_box_singular', array(
        'sanitize_callback' => 'absint'
    ));
}
function general_settings_section_callback()
{
    echo 'Configure your review plugin settings below:';
}
add_action('admin_init', 'general_settings_fields');
// general setting end

function styling_setting_page()
{
?>
<div class="wrap">
    <div class="card" style='max-width:100%'>
        <form method="post" action="options.php">
            <?php
                settings_fields('style_settings_group');
                do_settings_sections('style-settings');
                submit_button();
                ?>
        </form>
    </div>
</div>
<?php
}

function review_rating_icon_color_callback()
{
    $rating_icon_color = get_option('review_rating_icon_color', '#cccccccc');
?>
<input type="text" name="review_rating_icon_color" class="review-color-field"
    value="<?php echo esc_attr($rating_icon_color); ?>" data-default-color="#cccccc" />
<?php
}
function review_rating_icon_active_color_callback()
{
    $rating_icon_active_color = get_option('review_rating_icon_active_color', '#ffc700');
?>
<input type="text" name="review_rating_icon_active_color" class="review-color-field"
    value="<?php echo esc_attr($rating_icon_active_color); ?>" data-default-color="#ffc700" />
<?php
}
function review_rating_input_box_border_color_callback()
{
    $rating_input_box_border_color = get_option('review_rating_input_box_border_color', '#F85606');
?>
<input type="text" name="review_rating_input_box_border_color" class="review-color-field"
    value="<?php echo esc_attr($rating_input_box_border_color); ?>" data-default-color="#F85606" />
<?php
}
function review_rating_form_button_bg_callback()
{
    $review_rating_form_button_bg = get_option('review_rating_form_button_bg', '#F85606');
?>
<input type="text" name="review_rating_form_button_bg" class="review-color-field"
    value="<?php echo esc_attr($review_rating_form_button_bg); ?>" data-default-color="#F85606" />
<?php
}
function review_box_outer_border_callback()
{
    $box_outer_border = get_option('review_box_outer_border', '#e2eded');
?>
<input type="text" name="review_box_outer_border" class="review-color-field"
    value="<?php echo esc_attr($box_outer_border); ?>" data-default-color="#e2eded" />
<?php
}
function review_box_header_footer_background_callback()
{
    $box_header_footer_bg = get_option('review_box_header_footer_bg', '#f2f2f2');
?>
<input type="text" name="review_box_header_footer_bg" class="review-color-field"
    value="<?php echo esc_attr($box_header_footer_bg); ?>" data-default-color="#f2f2f2" />
<?php
}
function review_item_background_callback()
{
    $item_bg = get_option('review_item_bg', '#fff');
?>
<input type="text" name="review_item_bg" class="review-color-field" value="<?php echo esc_attr($item_bg); ?>"
    data-default-color="#fff" />
<?php
}
function final_score_percentage_bar_background_callback()
{
    $final_score_percentage_bar_bg = get_option('final_score_percentage_bar_bg', '030303');
?>
<input type="text" name="final_score_percentage_bar_bg" class="review-color-field"
    value="<?php echo esc_attr($final_score_percentage_bar_bg); ?>" data-default-color="#ff0000" />
<?php
}
function link_color_callback()
{
    $review_link_color = get_option('review_link_color', '#030303');
?>
<input type="text" name="review_link_color" class="review-color-field"
    value="<?php echo esc_attr($review_link_color); ?>" data-default-color="#030303" />
<?php
}
function link_decoration_callback()
{
    $review_link_decoration = get_option('review_link_decoration');
?>
<input type="text" name="review_link_decoration" class="review-color-field"
    value="<?php echo esc_attr($review_link_decoration); ?>" data-default-color="#ff0000" />
<?php
}
function link_color_on_mouse_over_callback()
{
    $review_link_color_on_mouse_over = get_option('review_link_color_on_mouse_over', '#ffc700');
?>
<input type="text" name="review_link_color_on_mouse_over" class="review-color-field"
    value="<?php echo esc_attr($review_link_color_on_mouse_over); ?>" data-default-color="#ffc700" />
<?php
}
function link_decoration_on_mouse_over_callback()
{
    $review_link_decoration_on_mouse_over = get_option('review_link_decoration_on_mouse_over');
?>
<input type="text" name="review_link_decoration_on_mouse_over" class="review-color-field"
    value="<?php echo esc_attr($review_link_decoration_on_mouse_over); ?>" data-default-color="#ff0000" />
<?php
}

function style_settings_fields()
{
    add_settings_section('style_settings_section', 'Style Settings', 'style_settings_section_callback', 'style-settings');
    add_settings_field('review_rating_icon_color', 'Review rating form icon Color', 'review_rating_icon_color_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_rating_icon_active_color', 'Review rating form icon active Color', 'review_rating_icon_active_color_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_rating_input_box_border_color', 'Review rating form input box border color', 'review_rating_input_box_border_color_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_rating_form_button_bg', 'Review rating form button Color', 'review_rating_form_button_bg_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_box_outer_border', 'Review Box Outer Border', 'review_box_outer_border_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_box_header_footer_bg', 'Review Box Header & Footer Background', 'review_box_header_footer_background_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_item_bg', 'Review Item Background', 'review_item_background_callback', 'style-settings', 'style_settings_section');
    add_settings_field('final_score_percentage_bar_bg', 'Final Score and Percentage bar Background', 'final_score_percentage_bar_background_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_link_color', 'Link Color', 'link_color_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_link_decoration', 'Link Decoration', 'link_decoration_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_link_color_on_mouse_over', 'Link color on mouse over', 'link_color_on_mouse_over_callback', 'style-settings', 'style_settings_section');
    add_settings_field('review_link_decoration_on_mouse_over', 'Link decoration on mouse over', 'link_decoration_on_mouse_over_callback', 'style-settings', 'style_settings_section');
    // Helper function to sanitize color values
    $sanitize_color = function ($color) {
        $color = sanitize_text_field($color);
        // Remove any whitespace
        $color = trim($color);
        // Validate hex color format
        if (
            preg_match('/#([a-fA-F0-9]{3}){1,2}\b/', $color) ||
            preg_match('/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[\d.]+\s*)?\)$/', $color)
        ) {
            return $color;
        }
        return '';
    };

    register_setting('style_settings_group', 'review_rating_icon_color', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_rating_icon_active_color', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_rating_input_box_border_color', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_rating_form_button_bg', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_box_outer_border', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_box_header_footer_bg', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_item_bg', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'final_score_percentage_bar_bg', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_link_color', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_link_decoration', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));
    register_setting('style_settings_group', 'review_link_color_on_mouse_over', array(
        'sanitize_callback' => $sanitize_color
    ));
    register_setting('style_settings_group', 'review_link_decoration_on_mouse_over', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));
}
function style_settings_section_callback()
{
    echo '';
}
add_action('admin_init', 'style_settings_fields');

function typography_setting_page()
{
?>
<div class="wrap">
    <div class="card" style='max-width:100%'>
        <form method="post" action="options.php">
            <?php
                settings_fields('typography_settings_group');
                do_settings_sections('typography-settings');
                ?>
            <?php submit_button(); ?>
        </form>
    </div>
</div>
<?php
}
function review_box_title_callback()
{
    $review_box_title_family = get_option('review_box_title_family', 'Arial, sans-serif');
    $google_fonts = array(
        'Arial, sans-serif' => 'Arial',
        'Helvetica, sans-serif' => 'Helvetica',
        'Times New Roman, serif' => 'Times New Roman',
        // Add more Google Font families here
    );
    $review_box_title_size = get_option('review_box_title_size', '25');
    $review_box_title_weight = get_option('review_box_title_weight', '600');
?>

<label for="review_box_title_size">Size(px):</label>
<input type="text" name="review_box_title_size" class="review_box_title_size"
    value="<?php echo esc_attr($review_box_title_size); ?>" />

<label for="review_box_title_weight">Weight:</label>
<input type="text" name="review_box_title_weight" class="review_box_title_weight"
    value="<?php echo esc_attr($review_box_title_weight); ?>" />
<label for="review_box_title_family">Select Family:</label>
<select name="review_box_title_family">
    <?php
        foreach ($google_fonts as $font => $font_name) {
            $selected = ($review_box_title_family === $font) ? 'selected' : '';
            echo '<option value="' . esc_attr($font) . '" ' . esc_attr($selected) . '>' . esc_html($font_name) . '</option>';
        }
        ?>
</select>

<?php
}

function review_box_header_footer_callback()
{
    $review_box_header_footer_family = get_option('review_box_header_footer_family', 'Arial, sans-serif');
    $google_fonts = array(
        'Arial, sans-serif' => 'Arial',
        'Helvetica, sans-serif' => 'Helvetica',
        'Times New Roman, serif' => 'Times New Roman',
        // Add more Google Font families here
    );
    $review_box_header_footer_size = get_option('review_box_header_footer_size', '19');
    $review_box_header_footer_weight = get_option('review_box_header_footer_weight', '500');
?>

<label for="review_box_header_footer_size">Size(px):</label>
<input type="text" name="review_box_header_footer_size" class="review_box_header_footer_size"
    value="<?php echo esc_attr($review_box_header_footer_size); ?>" />

<label for="review_box_header_footer_weight">Weight:</label>
<input type="text" name="review_box_header_footer_weight" class="review_box_header_footer_weight"
    value="<?php echo esc_attr($review_box_header_footer_weight); ?>" />
<label for="review_box_header_footer_family">Select Family:</label>
<select name="review_box_header_footer_family">
    <?php
        foreach ($google_fonts as $font => $font_name) {
            $selected = ($review_box_header_footer_family === $font) ? 'selected' : '';
            echo '<option value="' . esc_attr($font) . '" ' . esc_attr($selected) . '>' . esc_html($font_name) . '</option>';
        }
        ?>
</select>

<?php
}
function review_summary_callback()
{
    $review_summary_family = get_option('review_summary_family', 'Arial, sans-serif');
    $google_fonts = array(
        'Arial, sans-serif' => 'Arial',
        'Helvetica, sans-serif' => 'Helvetica',
        'Times New Roman, serif' => 'Times New Roman',
        // Add more Google Font families here
    );
    $review_summary_size = get_option('review_summary_size', '15');
    $review_summary_weight = get_option('review_summary_weight', '500');
?>

<label for="review_summary_size">Size(px):</label>
<input type="text" name="review_summary_size" class="review_summary_size"
    value="<?php echo esc_attr($review_summary_size); ?>" />

<label for="review_summary_weight">Weight:</label>
<input type="text" name="review_summary_weight" class="review_summary_weight"
    value="<?php echo esc_attr($review_summary_weight); ?>" />
<label for="review_summary_family">Select Family:</label>
<select name="review_summary_family">
    <?php
        foreach ($google_fonts as $font => $font_name) {
            $selected = ($review_summary_family === $font) ? 'selected' : '';
            echo '<option value="' . esc_attr($font) . '" ' . esc_attr($selected) . '>' . esc_html($font_name) . '</option>';
        }
        ?>
</select>

<?php
}
function total_score_callback()
{
    $total_score_family = get_option('total_score_family', 'Arial, sans-serif');
    $google_fonts = array(
        'Arial, sans-serif' => 'Arial',
        'Helvetica, sans-serif' => 'Helvetica',
        'Times New Roman, serif' => 'Times New Roman',
        // Add more Google Font families here
    );
    $total_score_size = get_option('total_score_size', '48');
    $total_score_weight = get_option('total_score_weight', '400');
?>

<label for="total_score_size">Size(px):</label>
<input type="text" name="total_score_size" class="total_score_size"
    value="<?php echo esc_attr($total_score_size); ?>" />

<label for="total_score_weight">Weight:</label>
<input type="text" name="total_score_weight" class="total_score_weight"
    value="<?php echo esc_attr($total_score_weight); ?>" />
<label for="total_score_family">Select Family:</label>
<select name="total_score_family">
    <?php
        foreach ($google_fonts as $font => $font_name) {
            $selected = ($total_score_family === $font) ? 'selected' : '';
            echo '<option value="' . esc_attr($font) . '" ' . esc_attr($selected) . '>' . esc_html($font_name) . '</option>';
        }
        ?>
</select>

<?php
}
function user_rating_icon_callback()
{
    $user_rating_icon_family = get_option('user_rating_icon_family', 'Arial, sans-serif');
    $google_fonts = array(
        'Arial, sans-serif' => 'Arial',
        'Helvetica, sans-serif' => 'Helvetica',
        'Times New Roman, serif' => 'Times New Roman',
        // Add more Google Font families here
    );
    $user_rating_icon_size = get_option('user_rating_icon_size', '15');
    $user_rating_icon_weight = get_option('user_rating_icon_weight', '900');
?>

<label for="user_rating_icon_size">Size(px):</label>
<input type="text" name="user_rating_icon_size" class="user_rating_icon_size"
    value="<?php echo esc_attr($user_rating_icon_size); ?>" />

<label for="user_rating_icon_weight">Weight:</label>
<input type="text" name="user_rating_icon_weight" class="user_rating_icon_weight"
    value="<?php echo esc_attr($user_rating_icon_weight); ?>" />
<label for="user_rating_icon_family">Select Family:</label>
<select name="user_rating_icon_family">
    <?php
        foreach ($google_fonts as $font => $font_name) {
            $selected = ($user_rating_icon_family === $font) ? 'selected' : '';
            echo '<option value="' . esc_attr($font) . '" ' . esc_attr($selected) . '>' . esc_html($font_name) . '</option>';
        }
        ?>
</select>

<?php
}
function typography_settings_fields()
{
    add_settings_section('typography_settings_section', 'Typography Settings', 'typography_settings_section_callback', 'typography-settings');
    add_settings_field('user_rating_icon', 'User Rating icon', 'user_rating_icon_callback', 'typography-settings', 'typography_settings_section');
    add_settings_field('review_box_title', 'Review box title', 'review_box_title_callback', 'typography-settings', 'typography_settings_section');
    add_settings_field('review_box_header_footer', 'Review box header & footer', 'review_box_header_footer_callback', 'typography-settings', 'typography_settings_section');
    add_settings_field('total_score', 'Average Total Score', 'total_score_callback', 'typography-settings', 'typography_settings_section');
    add_settings_field('review_summary', 'Review Summary', 'review_summary_callback', 'typography-settings', 'typography_settings_section');

    // Helper function to sanitize font family
    $sanitize_font_family = function ($font) {
        $allowed_fonts = array(
            'Arial, sans-serif',
            'Helvetica, sans-serif',
            'Times New Roman, serif'
        );
        $font = sanitize_text_field($font);
        return in_array($font, $allowed_fonts) ? $font : 'Arial, sans-serif';
    };

    // Helper function to sanitize font size and weight
    $sanitize_numeric = function ($value) {
        return absint($value);
    };

    // Register settings for typography
    register_setting('typography_settings_group', 'user_rating_icon_size', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'user_rating_icon_weight', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'user_rating_icon_family', array(
        'sanitize_callback' => $sanitize_font_family
    ));
    register_setting('typography_settings_group', 'review_box_title_size', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'review_box_title_weight', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'review_box_title_family', array(
        'sanitize_callback' => $sanitize_font_family
    ));
    register_setting('typography_settings_group', 'review_box_header_footer_size', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'review_box_header_footer_weight', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'review_box_header_footer_family', array(
        'sanitize_callback' => $sanitize_font_family
    ));
    register_setting('typography_settings_group', 'total_score_size', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'total_score_weight', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'total_score_family', array(
        'sanitize_callback' => $sanitize_font_family
    ));
    register_setting('typography_settings_group', 'review_summary_size', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'review_summary_weight', array(
        'sanitize_callback' => $sanitize_numeric
    ));
    register_setting('typography_settings_group', 'review_summary_family', array(
        'sanitize_callback' => $sanitize_font_family
    ));
}
function typography_settings_section_callback()
{
    echo '';
}
add_action('admin_init', 'typography_settings_fields');


function advanced_setting_page()
{
    echo '<div class="wrap">';
    echo '<h2>Advanced Settings</h2>';

    echo '<h2 class="nav-tab-wrapper review-advanced-setting-wrapper">';
    echo '<a class="nav-tab nav-tab-active" href="#tab-1">Review Format</a>';
    echo '<a class="nav-tab" href="#tab-2">Email Reminder</a>';
    echo '<a class="nav-tab" href="#tab-3">Order Count</a>';
    echo '<a class="nav-tab" href="#tab-4">Top Order Customer</a>';
    echo '</h2>';

    echo '<div class="tab-content">';
    echo '<div id="tab-1" class="tab-pane active">';
    advanced_tab_1_settings_page();
    echo '</div>';

    echo '<div id="tab-2" class="tab-pane">';
    advanced_tab_2_settings_page();
    echo '</div>';

    echo '<div id="tab-3" class="tab-pane">';
    advanced_tab_3_settings_page();
    echo '</div>';

    echo '<div id="tab-4" class="tab-pane">';
    advanced_tab_4_settings_page();
    echo '</div>';
    echo '</div>';

    echo '</div>';
}

function advanced_tab_1_settings_page()
{
?>
<div class="wrap">
    <form method="post" action="options.php">
        <?php
            settings_fields('advanced_tab1_settings_group');
            do_settings_sections('advanced-tab1-settings');
            submit_button();
            ?>
    </form>
</div>
<?php
}
function advanced_tab1_settings_fields()
{
    // Tab 1
    add_settings_section('advanced_setting_tab1_section', 'Review Formate Setting', 'advanced_setting_tab1_section_callback', 'advanced-tab1-settings');
    add_settings_field('review_rating_formate', 'Review Formate', 'review_rating_formate_callback', 'advanced-tab1-settings', 'advanced_setting_tab1_section');
    register_setting('advanced_tab1_settings_group', 'review_rating_formate', array(
        'sanitize_callback' => 'sanitize_text_field'
    ));
}

add_action('admin_init', 'advanced_tab1_settings_fields');
function advanced_setting_tab1_section_callback()
{
    echo '<p>Choose your preferred rating format:</p>';
}

function review_rating_formate_callback()
{
    $image_url = plugins_url('admin/images/', __FILE__);


    $rating_formate = get_option('review_rating_formate', 'half_star'); // Default is 'star'
    // Get image attachments for review formats
    $icon_choices = array(
        'no_rating_star' => wp_get_attachment_image(get_option('no_rating_star_image_id'), 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img')),
        'half_star' => wp_get_attachment_image(get_option('half_star_image_id'), 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img')),
        'full_star' => wp_get_attachment_image(get_option('full_star_image_id'), 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img')),
        'five_star' => wp_get_attachment_image(get_option('five_star_image_id'), 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img')),
    );

    // Fallback to direct image URLs if attachment IDs not set
    if (empty($icon_choices['no_rating_star'])) {
        $attachment_id = attachment_url_to_postid($image_url . 'no_rating.png');
        if ($attachment_id) {
            $icon_choices['no_rating_star'] = wp_get_attachment_image($attachment_id, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img'));
        } else {
            // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for admin settings images
            $icon_choices['no_rating_star'] = '<img class="user_img" src="' . esc_url($image_url . 'no_rating.png') . '" alt="review-format-img">';
        }
    }
    if (empty($icon_choices['half_star'])) {
        $attachment_id = attachment_url_to_postid($image_url . 'half_star.png');
        if ($attachment_id) {
            $icon_choices['half_star'] = wp_get_attachment_image($attachment_id, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img'));
        } else {
            // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for admin settings images
            $icon_choices['half_star'] = '<img class="user_img" src="' . esc_url($image_url . 'half_star.png') . '" alt="review-format-img">';
        }
    }
    if (empty($icon_choices['full_star'])) {
        $attachment_id = attachment_url_to_postid($image_url . 'full_star.png');
        if ($attachment_id) {
            $icon_choices['full_star'] = wp_get_attachment_image($attachment_id, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img'));
        } else {
            // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for admin settings images
            $icon_choices['full_star'] = '<img class="user_img" src="' . esc_url($image_url . 'full_star.png') . '" alt="review-format-img">';
        }
    }
    if (empty($icon_choices['five_star'])) {
        $attachment_id = attachment_url_to_postid($image_url . 'five_star.png');
        if ($attachment_id) {
            $icon_choices['five_star'] = wp_get_attachment_image($attachment_id, 'thumbnail', false, array('class' => 'user_img', 'alt' => 'review-format-img'));
        } else {
            // phpcs:ignore PluginCheck.CodeAnalysis.ImageFunctions.NonEnqueuedImage -- Fallback for admin settings images
            $icon_choices['five_star'] = '<img class="user_img" src="' . esc_url($image_url . 'five_star.png') . '" alt="review-format-img">';
        }
    }
?>
<?php foreach ($icon_choices as $value => $label) { ?>
<div class="input_group">
    <input type="radio" id="review_rating_formate<?php echo esc_attr($value); ?>" name="review_rating_formate"
        value="<?php echo esc_attr($value); ?>" <?php checked($rating_formate, $value); ?> />
    <label for="review_rating_formate<?php echo esc_attr($value); ?>"><?php echo wp_kses_post($label); ?></label><br>
</div>
<?php } ?>
<?php
}
function advanced_tab_2_settings_page()
{
?>
<div class="wrap">
    <form method="post" action="options.php">
        <?php
            settings_fields('advanced_tab2_settings_group');
            do_settings_sections('advanced-tab2-settings');
            submit_button();
            ?>
    </form>
</div>
<?php
}

function advanced_tab_3_settings_page()
{
    include(plugin_dir_path(__FILE__) . 'template/admin/order-count.php');
}

function advanced_tab_4_settings_page()
{
    include(plugin_dir_path(__FILE__) . 'template/admin/top-order-customer.php');
}
function advanced_tab2_settings_fields()
{
    // Tab 2
    add_settings_section('advanced_setting_tab2_section', 'Email Reminder', 'advanced_setting_tab2_section_callback', 'advanced-tab2-settings');
    add_settings_field('review_email_reminder', 'Set Reminder Time', 'review_email_reminder_callback', 'advanced-tab2-settings', 'advanced_setting_tab2_section');
    register_setting('advanced_tab2_settings_group', 'review_email_reminder', array(
        'sanitize_callback' => function ($value) {
            $allowed_values = array('3', '7');
            $value = sanitize_text_field($value);
            // Allow custom values that are positive integers
            if (!in_array($value, $allowed_values) && !ctype_digit($value)) {
                return '3'; // Default value
            }
            return $value;
        }
    ));
}

add_action('admin_init', 'advanced_tab2_settings_fields');
function advanced_setting_tab2_section_callback()
{
    echo '<p>Set your no review user reminder time:</p>';
}

function review_email_reminder_callback()
{
    $review_email_reminder = get_option('review_email_reminder', '3'); // Default is '3'
    $choices = array(
        '3' => 'Send reminder after 3 days',
        '7'  => 'Send reminder after 7 days',
    );
?>
<?php foreach ($choices as $value => $label) { ?>
<div class="input_group">
    <input type="radio" id="review_email_reminder<?php echo esc_attr($value); ?>" name="review_email_reminder"
        value="<?php echo esc_attr($value); ?>" <?php checked($review_email_reminder, $value); ?> />
    <label for="review_email_reminder<?php echo esc_attr($value); ?>"><?php echo wp_kses_post($label); ?></label><br>
</div>

<?php } ?>
<div class="input_group">
    <input type="text" id="review_email_reminder_custom" name="review_email_reminder"
        value="<?php echo ($review_email_reminder == '7' || $review_email_reminder == '3') ? '' : esc_attr($review_email_reminder); ?>" />
</div>
<?php
}
// Hook your function to 'woocommerce_payment_complete'
add_action('woocommerce_payment_complete', 'order_completed_custom_email_notification', 10, 1);

function order_completed_custom_email_notification($order_id)
{
    // Get the order object
    $order = wc_get_order($order_id);

    // Check if the order is valid and the status is "completed"
    if (is_a($order, 'WC_Order') && $order->has_status('completed')) {
        $current_user = wp_get_current_user();
        $user_email = $current_user->user_email;

        if (!empty($user_email)) {
            $to = sanitize_email($user_email);
            $subject = 'Your Order Is Complete';
            $message = 'Thank you for your order. This is a custom email for your completed order.';
            $headers = 'From: Your Name <<EMAIL>>' . "\r\n";

            // Send the email
            $sent = wp_mail($to, $subject, $message, $headers);

            if ($sent) {
                // Email sent successfully
                if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                    // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
                    error_log('Custom email sent successfully.');
                }
            } else {
                // Email sending failed
                if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                    // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_error_log
                    error_log('Failed to send the custom email.');
                }
            }
        }
    }
}


function enqueue_color_picker()
{
    // Enqueue WordPress color picker script and style.
    wp_enqueue_style('wp-color-picker');
    wp_enqueue_script('wp-color-picker');
}
add_action('admin_enqueue_scripts', 'enqueue_color_picker');

// Add this in your theme's functions.php or a custom plugin file
add_action('wp_ajax_submit_question_reply', 'submit_question_reply');
add_action('wp_ajax_nopriv_submit_question_reply', 'submit_question_reply');

function submit_question_reply()
{
    // Verify nonce
    if (!isset($_POST['question_reply_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['question_reply_nonce'])), 'submit_question_reply_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Validate required fields
    if (!isset($_POST['comment_id'], $_POST['reply_content'], $_POST['post_id'])) {
        wp_send_json_error('Missing required fields');
        return;
    }

    // Sanitize and validate input
    $post_id = isset($_POST['post_id']) ? intval(wp_unslash($_POST['post_id'])) : 0;
    $comment_id = isset($_POST['comment_id']) ? intval(wp_unslash($_POST['comment_id'])) : 0;
    $reply_content = isset($_POST['reply_content']) ? wp_kses_post(wp_unslash($_POST['reply_content'])) : '';

    if (!$post_id || !$comment_id || empty($reply_content)) {
        wp_send_json_error('Invalid input data');
        return;
    }
    $current_user_id = get_current_user_id();
    $current_user = get_user_by('ID', $current_user_id);
    $display_name = !isset($current_user->display_name) ? '' : $current_user->display_name;
    $user_email = !isset($current_user->user_email) ? '' : $current_user->user_email;
    $author_url = !isset($current_user->user_url) ? '' : $current_user->user_url;
    // Sanitize and validate data as needed
    $commentdata = array(
        'comment_post_ID' => $post_id,
        'comment_parent' => $comment_id,
        'comment_content' => $reply_content,
        'comment_approved' => 1, // Approve the comment by default
        'comment_author' => $display_name,
        'comment_author_email' => $user_email,
        'comment_author_url' => $author_url,
        'comment_type' => 'question_reply',
        'user_id' => $current_user_id,
    );
    // Insert the reply
    $reply_id = wp_insert_comment($commentdata);

    if ($reply_id) {
        // Return a response, e.g., a success message
        wp_send_json_success('question reply added successfully');
    } else {
        // Return a response, e.g., a success message
        wp_send_json_success('Failed to add reply.');
    }
    wp_die();
}



function status_update_question()
{
    // Verify nonce
    if (!isset($_POST['status_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['status_nonce'])), 'question_status_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Validate required fields
    if (!isset($_POST['question_id']) || !isset($_POST['status'])) {
        wp_send_json_error('Missing required fields');
        return;
    }

    // Sanitize and validate input
    $comment_id = isset($_POST['question_id']) ? intval(wp_unslash($_POST['question_id'])) : 0;
    $action = isset($_POST['status']) ? sanitize_text_field(wp_unslash($_POST['status'])) : '';

    if (!$comment_id || empty($action)) {
        wp_send_json_error('Invalid input data');
        return;
    }
    $status_mapping = array(
        'Approved' => '0',
        'Unapproved' => '1',
        'Spam' => 'spam',
        'Trash' => 'trash',
    );

    // Check if the provided action is valid.
    if (array_key_exists($action, $status_mapping)) {
        $comment_status = $status_mapping[$action];

        // Create an array with multiple parameters to send back.
        $response_data = array(
            'message' => 'Question status updated successfully.',
            'comment_id' => $comment_id,
            'status' => $action,
        );

        // Update the comment status.
        if (wp_set_comment_status($comment_id, $comment_status)) {
            $response_data['success'] = true;
        } else {
            $response_data['success'] = false;
            $response_data['message'] = 'Failed to update question status.';
        }

        // Send the JSON response with the array.
        wp_send_json($response_data);
    } else {
        wp_send_json_error('Invalid status action.');
    }
}

// Hook the above function to WordPress AJAX action
add_action('wp_ajax_status_update_question', 'status_update_question');
add_action('wp_ajax_nopriv_status_update_question', 'status_update_question');



// Add AJAX action to handle the comment update
add_action('wp_ajax_question_update', 'question_update_callback');
add_action('wp_ajax_nopriv_question_update', 'question_update_callback');

// Callback function to handle the comment update
function question_update_callback()
{
    // Verify nonce
    if (!isset($_POST['update_question_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['update_question_nonce'])), 'update_question_nonce')) {
        wp_send_json_error('Security check failed');
    }

    // Get the data sent via AJAX
    $comment_id = isset($_POST['commentId']) ? intval(wp_unslash($_POST['commentId'])) : 0;
    $question_content = isset($_POST['questionContent']) ? sanitize_text_field(wp_unslash($_POST['questionContent'])) : '';
    $user_name = isset($_POST['userName']) ? sanitize_text_field(wp_unslash($_POST['userName'])) : '';
    $email = isset($_POST['email']) ? sanitize_email(wp_unslash($_POST['email'])) : '';
    $ip = isset($_POST['ip']) ? sanitize_text_field(wp_unslash($_POST['ip'])) : '';

    // Update the comment data (replace with your own logic)
    if ($comment_id > 0) {
        $updated_data = array(
            'comment_ID' => $comment_id,
            'comment_content' => $question_content,
            'comment_author' => $user_name,
            'comment_author_email' => $email,
            'comment_author_IP' => $ip,
        );

        $updated = wp_update_comment($updated_data);
        // Create an array with multiple parameters to send back.
        $response_data = array(
            'message' => 'Question updated successfully.',
            'data' => $question_content,
            'comment_id' =>  $comment_id,
        );
        if ($updated) {
            $response_data['success'] = true;
        } else {
            $response_data['success'] = false;
            $response_data['message'] = 'Failed to update question status.';
        }
        // Send the JSON response with the array.
        wp_send_json($response_data);
    } else {
        echo json_encode(array('success' => false, 'message' => 'Invalid comment ID.'));
    }

    wp_die();
}



// view reply
add_action('wp_ajax_get_question_replies', 'get_question_replies');
add_action('wp_ajax_nopriv_get_question_replies', 'get_question_replies');

function get_question_replies()
{
    // Verify nonce
    if (!isset($_POST['question_replies_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['question_replies_nonce'])), 'get_question_replies_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    if (isset($_POST['question_id'])) {
        $questionId = intval(wp_unslash($_POST['question_id']));
        $args = array(
            'parent' => $questionId,
            'status' => 'approve',
            'order' => 'ASC',
            'type' => 'question_reply',
        );
        $replies = get_comments($args);
        if (!empty($replies)) {
            foreach ($replies as $reply) {
                echo '<textarea class="wp-editor-area"
                 rows="2" cols="20"
                  name="update-qustion-content"
                   id="update-qustion-content">
                   ' . esc_textarea($reply->comment_content) . '
                   </textarea>';
            }
        } else {
            echo 'No replies found.';
        }
    }
    wp_die();
}

function mailtrap($phpmailer)
{
    // Only configure mailtrap if credentials are defined
    if (!defined('REVIEW_SMTP_HOST') || !defined('REVIEW_SMTP_USER') || !defined('REVIEW_SMTP_PASS')) {
        return;
    }

    $phpmailer->isSMTP();
    $phpmailer->Host = REVIEW_SMTP_HOST;
    $phpmailer->SMTPAuth = true;
    $phpmailer->Port = defined('REVIEW_SMTP_PORT') ? REVIEW_SMTP_PORT : 2525;
    $phpmailer->Username = REVIEW_SMTP_USER;
    $phpmailer->Password = REVIEW_SMTP_PASS;
}

add_action('phpmailer_init', 'mailtrap');


function send_emails_callback()
{
    // Verify nonce
    if (!isset($_POST['email_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['email_nonce'])), 'send_emails_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    // Validate order_ids
    if (!isset($_POST['order_ids']) || !is_array($_POST['order_ids'])) {
        wp_send_json_error('Invalid order IDs');
        return;
    }

    $order_ids = array_map('intval', wp_unslash($_POST['order_ids']));
    if (empty($order_ids)) {
        wp_send_json_error('No valid order IDs provided');
        return;
    }

    foreach ($order_ids as $order_id) {
        // Validate order exists
        $order = wc_get_order($order_id);
        if (!$order) {
            continue;
        }

        $user_id = $order->get_customer_id();
        $user = get_user_by('ID', $user_id);
        if (!$user || !is_email($user->user_email)) {
            continue;
        }

        // Get email template data
        $title = apply_filters('review_email_title', __('Review Request', 'review-next-for-woocommerce'));
        $from_email = sanitize_email(get_option('admin_email'));
        // Prepare email content
        $message = apply_filters('review_email_content', sprintf(
            /* translators: 1: customer name, 2: order number */
            __('Dear %1$s, please review your recent order #%2$s', 'review-next-for-woocommerce'),
            esc_html($user->display_name),
            esc_html($order_id)
        ));

        $to = $user->user_email;
        $subject = apply_filters('review_email_subject', sprintf(
            /* translators: %s: site name */
            __('[%s] Review Request', 'review-next-for-woocommerce'),
            wp_specialchars_decode(get_bloginfo('name'), ENT_QUOTES)
        ));

        $headers = array();
        $headers[] = 'Content-Type: text/html; charset=UTF-8';
        $headers[] = sprintf('From: %s <%s>', wp_specialchars_decode(get_bloginfo('name'), ENT_QUOTES), $from_email);
        $headers[] = sprintf('Reply-To: %s', $from_email);        // Send email
        $sent = wp_mail($to, $subject, $message, $headers);

        // Track email status
        if ($sent) {
            update_post_meta($order_id, '_review_reminder_sent', current_time('mysql'));
            do_action('review_reminder_email_sent', $order_id, $user->ID);
        } else {
            update_post_meta($order_id, '_review_reminder_failed', current_time('mysql'));
            do_action('review_reminder_email_failed', $order_id, $user->ID);
        }
    }

    // Return a response to the JavaScript
    wp_send_json_success();
}

add_action('wp_ajax_send_emails', 'send_emails_callback');