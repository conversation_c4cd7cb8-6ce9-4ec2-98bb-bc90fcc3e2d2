<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

global $product;
$product_id = $product->get_id();
$comments_per_page = 5;
$page = get_query_var('paged') ? get_query_var('paged') : 1; // Get the current page or default to page 1
$questions = get_comments(
  array(
    'post_id' => $product->get_id(),
    'status' => 'approve',
    // Display approved comments
    'number' => $comments_per_page,
    // Limit the number of comments to 15
    'order' => 'DESC',
    // Order by most recent comments
    'type' => 'question',
    'offset' => ($page - 1) * $comments_per_page,
  )
);
$args = array(
  'post_id' => $product->get_id(),
  'type' => 'question',
  'count' => true // return only the count
);
// Get the number of reviews for the product
$question_count = get_comments($args);
$args = array(
  'post_id' => $product->get_id(),
  'type' => 'question_reply',
  'count' => true // return only the count
);
// Get the number of reviews for the product
$question_reply_count = get_comments($args);


// Customize your tab title here (e.g., "Custom Tab (X)")
echo '<div class="user_question_section" id="user_question">';
echo '<h2 class="title">Questions About This Product (' . esc_html($question_count) . ')</h2>';
if (is_user_logged_in()) {
  // Output the form HTML here with your specific requirements
  echo '<form id="question-form"  method="post">';
  echo '<div class="input_group" id="question_group">';
  echo '<input type="hidden" name="comment_post_ID" value="' . esc_attr($product_id) . '" id="comment_post_ID">';
  echo '<input type="hidden" name="comment_parent" id="comment_parent" value="0">';
  echo '<textarea class="question_field" name="question" value="" id="question" placeholder="Ask a Question" required></textarea>';
  echo '<p id="error-message" class="">Your question should not contain contact information such as email, phone or external web links. Visit "My Orders" if you have questions about your previous order. </p>';
  echo '<input class="submit_btn" type="submit" value="ASK QUESTION">';
  echo '</div>';
  echo '</form>';
} else {
  // Display login and registration links with a redirect parameter
  $current_url = isset($_SERVER['REQUEST_URI']) ? esc_url_raw(wp_unslash($_SERVER['REQUEST_URI'])) : '';
  $login_url = wp_login_url(esc_url($current_url));
  $registration_url = wp_registration_url();

  $login_link = '<a href="' . esc_url($login_url) . '">Login</a>';
  $register_link = '<a href="' . esc_url($registration_url) . '">Register</a>';

  $login_message = 'Please ' . $login_link . ' or ' . $register_link . ' to ask a question.';

  echo '<p class="login_redirect">' . wp_kses_post($login_message) . '</p>';
}
echo '<div class="user_question_show">';
echo '<h4 class="short_title">Other questions answered by Admin (' . esc_html($question_reply_count) . ')</h4>';
echo '<div class="card">';

// Get the question icon image
$question_icon_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/images/problem.png';
$question_icon_id = review_plugin_get_attachment_id_by_path($question_icon_path, 'problem.png');
foreach ($questions as $question) {
  $authorUsername = get_comment_author($question->comment_ID);
  $questionDate = gmdate('F j, Y', strtotime($question->comment_date)); // Format the comment date
  $currentUserId = get_current_user_id();
  echo '<div class="item">';
  echo '<div class="short_content">';
  if ($question_icon_id) {
    echo wp_get_attachment_image($question_icon_id, 'thumbnail', false, array('class' => 'question_icon', 'alt' => 'question-icon'));
  } else {
    // Fallback if attachment ID not found, use WooCommerce placeholder
    echo wp_kses_post(wc_placeholder_img('thumbnail', array('class' => 'question_icon', 'alt' => 'Question Icon Placeholder')));
  }
  echo '<p>' . esc_html($question->comment_content) . '</p>';
  echo '</div>';
  echo '<div class="question_dropdown" id="question_dropdown' . esc_attr($question->comment_ID) . '" >';
  if (current_user_can('administrator')) {
    echo '<h5 class="question-reply-trigger" target="' . esc_attr($question->comment_ID) . '"><i class="fas fa-reply"></i></span> Reply</h5>';
  }
  echo '</div>';
  echo '<div class="content">';
  echo '<p class="create_date">' . esc_html($questionDate) . '</p>';
  echo '</div>';
  echo '</div>';
  echo '<form class="reply_question_fm" id="reply_question_form' . esc_attr($question->comment_ID) . '" method="post" style="display:none">';
  echo '<div class="input_group" id="question_group">';
  echo '<input type="hidden" name="comment_post_ID" value="' . esc_attr($product_id) . '" id="comment_post_ID">';
  echo '<input type="hidden" name="comment_parent" id="comment_parent" value="' . esc_attr($question->comment_ID) . '">';
  echo '<textarea class="question_field" name="question_reply" value="" id="reply_question_field" placeholder="Ask a Question" required></textarea>';
  echo '<p id="reply-error-message" class="">Your question should not contain contact information such as email, phone or external web links. Visit "My Orders" if you have questions about your previous order. </p>';
  echo '<input class="submit_btn" type="submit" value="ASK QUESTION">';
  echo '</div>';
  echo '</form>';

  // Get and output the replies to this comment
  $args = array(
    'parent' => $question->comment_ID,
    'status' => 'approve',
    'order' => 'ASC',
    'type' => 'question_reply',
  );
  $question_replies = get_comments($args);
  // Get the answer icon image
  $answer_icon_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/images/answer.png';
  $answer_icon_id = review_plugin_get_attachment_id_by_path($answer_icon_path, 'answer.png');
  foreach ($question_replies as $question_reply) {
    $questionDate = gmdate('F j, Y', strtotime($question_reply->comment_date));
    $authorUsername = get_comment_author($question_reply->comment_ID);
    $questionDate = gmdate('F j, Y', strtotime($question_reply->comment_date)); // Format the comment date
    $currentUserId = get_current_user_id();
    echo '<div class="item" style="padding-left:25px">';
    echo '<div class="short_content">';
    if ($answer_icon_id) {
      echo wp_get_attachment_image($answer_icon_id, 'thumbnail', false, array('class' => 'question_icon', 'alt' => 'answer-icon'));
    } else {
      // Fallback if attachment ID not found, use WooCommerce placeholder
      echo wp_kses_post(wc_placeholder_img('thumbnail', array('class' => 'question_icon', 'alt' => 'Answer Icon Placeholder')));
    }
    echo '<p>' . esc_html($question_reply->comment_content) . '</p>';
    echo '</div>';
    echo '<div class="question_dropdown" id="question_dropdown' . esc_attr($question_reply->comment_ID) . '" >';
    echo '</div>';
    echo '<div class="content">';
    echo '<p class="create_date">' . esc_html($questionDate) . '</p>';
    echo '</div>';
    echo '</div>';
  }
}
// Pagination links
// $total_comments = get_comments_number($product->get_id());
// if ($total_comments > $comments_per_page) {
//     echo '<div class="pagination">';
//     echo paginate_comments_links(array(
//         'total' => ceil($total_comments / $comments_per_page),
//         'current' => $page,
//     ));
//     echo '</div>';
// }

echo '</div>';
echo '</div>';
echo '</div>';
