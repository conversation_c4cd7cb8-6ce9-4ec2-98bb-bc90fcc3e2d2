<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

global $product;

$comments = get_comments(array(
    'post_id' => $product->get_id(),
    'status' => 'approve', // Display approved comments
    'number' => 15, // Limit the number of comments to 15
    'order' => 'DESC', // Order by most recent comments
    'type' => 'review',
));

// Get the product ID
$product_id = $product->get_id();
$product = wc_get_product($product_id);
?>
<div class='product_review_show' id='product_review_show' data-product-id="<?php echo esc_attr($product_id); ?>">
    <!-- filter mode -->
    <div class='mode-filter-sort'>
        <?php
        if ($product) {
            // Get the comment count for the product
            $total_reviews = $product->get_review_count();

            if ($total_reviews > 0) {
                // Display the total number of reviews and filtering options
                echo '<h2 class="title">' . esc_html($total_reviews) . ' reviews for ' . esc_html($product->get_name()) . '</h2>';

                // Display the filtering options
                echo '<div class="sort-mode">';
                echo '<div class="most-recent">';
                echo '<i class="fas fa-random"></i>';
                echo '<select id="sort-order" class="slct">';
                echo '<option value="recent">Recent</option>';
                echo '<option value="high_to_low">High to Low Rating</option>';
                echo '<option value="low_to_high">Low to High Rating</option>';
                echo '</select>';
                echo '</div>';

                echo '<div class="all-ratng">';
                echo '<i class="fas fa-filter"></i>';
                echo '<select id="rating-filter" class="slct">';
                echo '<option value="">All Rating</option>';
                echo '<option value="1">One Star</option>';
                echo '<option value="2">Two Star</option>';
                echo '<option value="3">Three Star</option>';
                echo '<option value="4">Four Star</option>';
                echo '<option value="5">Five Star</option>';
                echo '</select>';
                echo '</div>';

                echo '</div>'; // Close sort-mode

                echo '<div class="filter-mode">';
                echo '<div class="fil-verified">';
                echo '<input type="checkbox" name="filter_verified_owner" id="verified_owner">';
                echo '<label for="verified_owner">Filter By Verified Owner</label>';
                echo '</div>';

                echo '<div class="fil-images-videos">';
                echo '<input type="checkbox" name="filter_images_video" id="images_video">';
                echo '<label for="images_video">Review with images and videos</label>';
                echo '</div>';

                echo '</div>';
                echo '<span class="loader" id="loader"></span>';
            }
        }
        ?>
    </div>

    <!-- review item -->
    <div id="container-review">

    </div>

</div>