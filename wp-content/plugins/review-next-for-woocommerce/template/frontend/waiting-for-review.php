<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

// Get the current user's ID
$current_user_id = get_current_user_id();

// Define the arguments for the order query
// Using a more efficient query approach without meta_key/meta_value
$args = array(
      'numberposts' => -1,
      // Get all orders
      'post_type' => 'shop_order',
      'post_status' => array('wc-delivered'),
      // Use customer_id parameter instead of meta_key/meta_value for better performance
      'customer_id' => $current_user_id,
);

// Get the orders based on the query
$orders = wc_get_orders($args);
?>

<!-- waiting for review -->
<?php
if (count($orders) > 0) {
      echo '<div class="waiting-for-review">';
      echo '<h2 class="title">Waiting For Review</h2>';
      echo '<div class="cards">';
      // // Loop through the orders
      foreach ($orders as $order) {
            $order_id = $order->get_id();
            $order_status = $order->get_status();

            // Get order items
            foreach ($order->get_items() as $item_id => $item) {
                  $product_id = $item->get_product_id();
                  $product = $item->get_product(); // see link above to get $product info

                  $comments = get_comments(
                        array(
                              'post_id' => $product_id,
                              // Use the order ID here
                              'type' => 'review',
                        )
                  );
                  if (empty($comments)) {
                        // No comment exists for this product in the order
                        echo '<div class="item">';
                        // Use wp_get_attachment_image for the product thumbnail
                        if (has_post_thumbnail($product_id)) {
                              $thumbnail_id = get_post_thumbnail_id($product_id);
                              echo wp_get_attachment_image($thumbnail_id, 'thumbnail', false, array(
                                    'class' => 'product_image',
                                    'alt' => esc_attr($product->get_name()) . ' Image'
                              ));
                        } else {
                              // Fallback if no thumbnail is set
                              echo wp_kses_post(wc_placeholder_img('thumbnail', array('class' => 'product_image', 'alt' => 'Product Image Placeholder')));
                        }
                        echo '<div>';
                        echo '<h3 class="product_name">' . esc_html($product->get_name()) . '</h3>';
                        echo '<a class="provide_link" href="' . esc_url(get_permalink($product->get_id())) . '">Provide Review</a>';
                        echo '</div>';
                        echo '</div>';
                  }
            }
      }
      echo '</div>';
      echo '</div>';
}
?>