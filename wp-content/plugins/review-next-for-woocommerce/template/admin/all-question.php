
<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

if (!class_exists('WP_List_Table')) {
    require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
}

class REVINEFO_Custom_Table_Plugin extends WP_List_Table
{
    public function __construct()
    {
        parent::__construct(array(
            'singular' => 'product_question',
            'plural'   => 'product_questions',
            'ajax'     => true,
        ));
    }

    public function prepare_items()
    {
        // Define the columns and data
        $columns = $this->get_columns();
        $hidden = array();
        $sortable = $this->get_sortable_columns();
        $data = $this->table_data();
        // Get the current status filter, default to 'all'
        $current_status = isset($_GET['question_status']) ? sanitize_text_field(wp_unslash($_GET['question_status'])) : 'all';

        // Verify nonce if this is a form submission
        if (isset($_POST['_wpnonce'])) {
            $nonce = sanitize_text_field(wp_unslash($_POST['_wpnonce']));
            if (!wp_verify_nonce($nonce, 'bulk-product_questions')) {
                wp_die('Security check failed. Please try again.');
            }
        }

        // Create an array of comment statuses and their corresponding counts
        $question_statuses = array(
            'all' => count($data),
            'pending' => 0,  // Initialize to 0
            'approved' => 0, // Initialize to 0
            'spam' => 0,     // Initialize to 0
            'trash' => 0,    // Initialize to 0
        );

        // Calculate counts for statuses other than 'all'
        foreach ($data as $item) {
            if ($item['status'] === 'hold') {
                $question_statuses['pending']++;
            } elseif ($item['status'] === 'approve') {
                $question_statuses['approved']++;
            } elseif ($item['status'] === 'spam') {
                $question_statuses['spam']++;
            } elseif ($item['status'] === 'trash') {
                $question_statuses['trash']++;
            }
        }

        // Output the sub-navigation menu
        echo '<ul class="subsubsub">';
        foreach ($question_statuses as $status => $count) {
            echo '<li class="' . esc_attr($status) . '">';
            if ($status === $current_status) {
                echo '<a href="#" class="current" aria-current="page">';
            } else {
                echo '<a href="' . esc_url(add_query_arg('question_status', $status)) . '">';
            }
            echo esc_html(ucfirst($status)) . ' <span class="count">(<span class="' . esc_attr($status) . '-count">' . esc_html($count) . '</span>)</span></a>';
            if ($status !== 'trash') {
                echo ' |';
            }
            echo '</li>';
        }
        echo '</ul';

        // Filter the data based on the current status
        if ($current_status !== 'all') {
            $data = array_filter($data, function ($item) use ($current_status) {
                return $item['status'] === $current_status;
            });
        }
        // Get the search query from the form submission
        // Nonce is already verified above for POST requests
        $search_query = isset($_POST['s']) ? sanitize_text_field(wp_unslash($_POST['s'])) : '';

        // If a search query is provided, filter the data
        if (!empty($search_query)) {
            $data = array_filter($this->table_data(), function ($item) use ($search_query) {
                return stripos($item['question_content'], $search_query) !== false;
            });
        } else {
            $data = $this->table_data();
        }
        // Get the current page number and items per page from the URL
        $current_page = $this->get_pagenum();
        $items_per_page = 10; // You can adjust the number of items per page

        // Calculate the total number of items
        $total_items = count($this->table_data());

        // Create a paginated data array based on the current page
        $data = array_slice($this->table_data(), (($current_page - 1) * $items_per_page), $items_per_page);

        // Set up the pagination
        $this->set_pagination_args(array(
            'total_items' => $total_items,
            'per_page'    => $items_per_page,
        ));
        $this->_column_headers = array($columns, $hidden, $sortable);
        $this->items = $data;
    }

    public function get_columns()
    {
        return array(
            'id' => 'ID',
            'user_name' => 'User Name',
            'question_content' => 'Question Content',
            'product' => 'Product',
            'question_date'  => 'Question Date',
            'view_reply'  => 'View Reply',
        );
    }

    public function get_sortable_columns()
    {
        return array(
            'id' => array('id', false),
            'user_name' => array('user_name', false),
            'product' => 'Product',
            'question_date' => array('question_date', false),
            'view_reply'  => array('view_reply', false),
        );
    }

    private function table_data()
    {
        $args = array(
            'status' => array('approve', 'hold'),
            'order' => 'ASC',
            'type' => 'question',
        );
        $questions = get_comments($args);
        $data = array();
        foreach ($questions as $question) {
            $product = wc_get_product($question->comment_post_ID);
            $questionDate = gmdate('F j, Y', strtotime($question->comment_date));
            $authorUsername = get_comment_author($question->comment_ID);
            $authorEmail = get_comment_author_email($question->comment_ID);
            $authorIP = get_comment_author_IP($question->comment_ID);
            $product = wc_get_product($question->comment_post_ID);
            $product_name = $product ? $product->get_name() : 'N/A';
            // Get the product URL
            $product_url = $product ? get_permalink($product->get_id()) : '';
            $view_product_link = $product_url ? '<a href="' . esc_url($product_url) . '">View Product</a>' : 'N/A';
            $args = array(
                'parent' => $question->comment_ID,
                'status' => 'approve',
                'order' => 'ASC',
                'type' => 'question_reply',
                'count' => true
            );
            $replyCount = get_comments($args);
            $data[] = array(
                'id' => $question->comment_ID,
                'product_id' => $product->get_id(),
                'user_name' => '<span id="username">' . $authorUsername . '</span>' . '<br>' . '<span id="user-email">' . $authorEmail . '</span>' . '<br>' . '<span id="user-ip">' . $authorIP . '</span>',
                'question_content' => '<span id="question_content" class="question_content_' . $question->comment_ID . '">' . $question->comment_content . '</span>',
                'product' => $product_name . '<br>' . $view_product_link,
                'question_date' => $questionDate,
                'status' => $question->comment_approved,
                'view_reply' => '<span class="post-com-count-wrapper post-com-count-' . $question->comment_ID . '">
                <a href="#" class="post-com-count post-com-count-approved">
                <span data-question-id="' . $question->comment_ID . '" class="view-reply dashicons dashicons-visibility"></span>
                <span class="dashicons dashicons-admin-comments"></span>
                ' . $replyCount . '
                </a>
            </span>',

            );
        }

        return $data;
    }

    public function column_default($item, $column_name)
    {

        return $item[$column_name];
    }

    public function column_product_name($item)
    {
        return $item['product_name'];
    }

    public function column_question_date($item)
    {
        return $item['question_date'];
    }



    public function column_question_content($item)
    {
        $comment_status = $item['status'] === '1' ? 'Approved' : 'Unapproved';

        $actions = array(
            $comment_status => '<a href="#" class="status_update_question" data-question-id="' . $item['id'] . '" data-action="' . $comment_status . '">' . $comment_status . '</a>',
            'reply' => '<button type="button" data-comment-id="' . $item['id'] . '" data-post-id="' . $item['product_id'] . '" data-action="replyto" class="vim-r-question-reply comment-inline button-link" aria-expanded="false" aria-label="Reply to this review">Reply</button>',
            'quick_edit' => '<button type="button" data-comment-id="' . $item['id'] . '" data-post-id="' . $item['product_id'] . '" data-action="question_edit" class="vim-q comment-inline button-link" aria-expanded="false" aria-label="Quick edit this review inline">Quick Edit</button>',
            'spam' => '<a href="#" class="spam-question status_update_question" data-question-id="' . $item['id'] . '" data-action="Spam">Spam</a>',
            'trash' => '<a href="#" class="trash-question status_update_question" data-question-id="' . $item['id'] . '" data-action="Trash">Trash</a>',
        );

        return sprintf('%1$s %2$s', $item['question_content'],  $this->row_actions($actions));
    }
}



function revinefo_custom_table_plugin_page()
{
    echo '<div class="wrap"><h2>Question list</h2>';
    echo '<form method="post" action="">';
    wp_nonce_field('bulk-product_questions', '_wpnonce', true);

    $custom_table = new REVINEFO_Custom_Table_Plugin();
    $custom_table->prepare_items();
    $custom_table->display();

    echo '</form>';
    echo '</div>';
}

revinefo_custom_table_plugin_page();
