<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

// Load WordPress
require_once('wp-load.php');

// Get the question ID from the URL parameter 'question_id'
$question_id = isset($_GET['question_id']) ? intval($_GET['question_id']) : 0;

// Get the question data based on the ID
$question = get_post($question_id);

// Check if the question exists
if (!$question || $question->post_type !== 'question') {
    echo "Question not found!";
    exit;
}

// Check user capability
if (!current_user_can('manage_options')) {
    wp_die('Access Denied.'); // Display an error message and exit
}

// Handle form submission (update question)
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify nonce for security
    if (!isset($_POST['question_edit_nonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['question_edit_nonce'])), 'edit_question_' . $question_id)) {
        wp_die('Security check failed. Please try again.');
    }

    // Get the updated question content from the form
    if (isset($_POST['question_content'])) {
        $updated_content = sanitize_text_field(wp_unslash($_POST['question_content']));

        // Update the question content in the database
        $question->post_content = $updated_content;
        wp_update_post($question);

        // Redirect back to the question list or another appropriate page
        wp_safe_redirect(admin_url('admin.php?page=your-plugin-page'));
        exit;
    }
}

// Display the Edit Question Form
get_header();
?>
<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <article id="post-<?php echo esc_attr($question->ID); ?>" <?php post_class(); ?>>
            <header class="entry-header">
                <h1 class="entry-title">Edit Question</h1>
            </header>
            <div class="entry-content">
                <form method="post" action="">
                    <?php wp_nonce_field('edit_question_' . $question_id, 'question_edit_nonce'); ?>
                    <textarea name="question_content" rows="5" cols="50"><?php echo esc_textarea($question->post_content); ?></textarea>
                    <br>
                    <input type="submit" name="submit" value="Update Question">
                </form>
            </div>
        </article>
    </main>
</div>
<?php
get_footer();
