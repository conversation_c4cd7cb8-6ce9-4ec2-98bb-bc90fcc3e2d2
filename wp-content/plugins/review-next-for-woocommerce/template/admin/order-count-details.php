<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

// Verify nonce for security
if (isset($_GET['page']) && $_GET['page'] == 'order-details' && isset($_GET['order_id'])) {
    // Check nonce for security
    if (!isset($_GET['_wpnonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_GET['_wpnonce'])), 'view_order_details')) {
        wp_die('Security check failed. Please try again.');
    }

    // Check user capability
    if (!current_user_can('manage_options')) {
        wp_die('Access Denied.'); // Display an error message and exit
    }

    $order_id = sanitize_text_field(wp_unslash($_GET['order_id']));


    // Get the order details based on the order ID
    $order = wc_get_order($order_id);

    // Check if the order exists
    if ($order) {
        $user_id = $order->get_customer_id();
        $orders = wc_get_orders(array(
            'customer' => $user_id,
            'status'   => array('completed'),
        ));

        $total_orders = count($orders);

        if ($total_orders > 0) {
            echo '<div class="wrap"><h2>User All Orders(' . esc_html($total_orders) . ')</h2>';

            foreach ($orders as $order) {
                $order_id = $order->get_id();
                $order_date = gmdate('F j, Y', strtotime($order->get_date_created()));
                $order_status = $order->get_status();
                $order_total = $order->get_total();

                echo '<h3>Order ID: ' . esc_html($order_id) . '</h3>';
                echo '<p><strong>Date:</strong> ' . esc_html($order_date) . '</p>';
                echo '<p><strong>Status:</strong> ' . esc_html($order_status) . '</p>';
                echo '<p><strong>Total:</strong> ' . esc_html($order_total) . '</p>';

                // Display order items
                $order_items = $order->get_items();

                if (!empty($order_items)) {
                    echo '<table class="wp-list-table widefat fixed striped">';
                    echo '<thead><tr>';
                    echo '<th>Product</th>';
                    echo '<th>Quantity</th>';
                    echo '<th>Price</th>';
                    echo '<th>Reviews</th>';
                    echo '</tr></thead><tbody>';

                    foreach ($order_items as $item_id => $item) {
                        $product_name = $item->get_name();
                        $quantity = $item->get_quantity();
                        $item_total = $item->get_total();
                        $product_id = $item->get_product_id();

                        echo '<tr>';
                        echo '<td>' . esc_html($product_name) . '</td>';
                        echo '<td>' . esc_html($quantity) . '</td>';
                        echo '<td>' . wp_kses_post(wc_price($item_total)) . '</td>';
                        echo '<td>' . wp_kses_post(revinefo_get_product_reviews_html($product_id)) . '</td>';
                        echo '</tr>';
                    }

                    echo '</tbody></table>';
                } else {
                    echo '<p>No items found for this order.</p>';
                }

                echo '<hr>'; // Add a horizontal line between orders
            }

            echo '</div>';
        } else {
            // Display a message if the customer has no orders
            echo '<div class="wrap"><p>No orders found for the specified customer.</p></div>';
        }
    }


    // If the order or user is not found
    echo '<div class="wrap">';
    echo '<a href="?page=review-advanced-setting" class="button">&laquo; Back To Advanced Setting</a>';
    echo '</div>';
}

function revinefo_get_product_reviews_html($product_id)
{
    $product = wc_get_product($product_id);
    $reviews = get_comments(array(
        'post_id' => $product->get_id(),
        'status' => 'approve',
        'type' => 'review',
        'number' => 5,
        'order' => 'DESC',
    ));

    $reviews_html = '';

    foreach ($reviews as $review) {
        $reviews_html .= '<p><strong>Author:</strong> ' . esc_html($review->comment_author) . '</p>';
        $reviews_html .= '<p><strong>Rating:</strong> ' . esc_html(get_comment_meta($review->comment_ID, 'rating', true)) . '</p>';
        $reviews_html .= '<p><strong>Comment:</strong> ' . esc_html($review->comment_content) . '</p>';
        $reviews_html .= '<hr>';
    }

    return $reviews_html;
}
