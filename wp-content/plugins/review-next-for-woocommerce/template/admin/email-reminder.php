<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

if (!class_exists('WP_List_Table')) {
    require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
}

class REVINEFO_Email_Reminder_Table_Plugin extends WP_List_Table
{
    public function __construct()
    {
        parent::__construct(array(
            'singular' => 'email-reminder',
            'plural'   => 'email-reminders',
            'ajax'     => true,
        ));

        add_action('init', array($this, 'schedule_cron_job'));

        // Hook the event to the function that will check and send emails
        add_action('send_email_cron_job', array($this, 'check_and_send_review_emails'));

        // Add a custom cron schedule for every minute
        add_filter('cron_schedules', array($this, 'add_every_minute_schedule'));
    }

    public function schedule_cron_job()
    {
        if (!wp_next_scheduled('send_review_reminder_emails')) {
            wp_schedule_event(time(), 'every_minute', 'send_review_reminder_emails');
        }
    }

    public static function add_every_minute_schedule($schedules)
    {
        $schedules['every_minute'] = array(
            'interval' => 60, // 60 seconds, every minute
            'display'  => __('Every Minute', 'review-next-for-woocommerce'),
        );
        // Log information for debugging
        // error_log('Custom cron schedule added: ' . print_r($schedules, true));
        return $schedules;
    }
    public function prepare_items()
    {
        // Define the columns and data
        $columns = $this->get_columns();
        $hidden = array();
        $sortable = $this->get_sortable_columns();
        $data = $this->table_data();
        $this->process_bulk_action();
        // Verify nonce for form submission
        if (isset($_POST['_wpnonce']) && wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['_wpnonce'])), 'bulk-' . $this->_args['plural'])) {
            // Get the search query from the form submission and sanitize it
            $search_query = isset($_POST['s']) ? sanitize_text_field(wp_unslash($_POST['s'])) : '';
        } else {
            $search_query = '';
        }

        // If a search query is provided, filter the data
        if (!empty($search_query)) {
            $data = array_filter($this->table_data(), function ($item) use ($search_query) {
                return stripos($item['order_id'], $search_query) !== false;
            });
        } else {
            $data = $this->table_data();
        }
        // Get the current page number and items per page from the URL
        $current_page = $this->get_pagenum();
        $items_per_page = 10; // You can adjust the number of items per page

        // Calculate the total number of items
        $total_items = count($this->table_data());

        // Create a paginated data array based on the current page
        $data = array_slice($this->table_data(), (($current_page - 1) * $items_per_page), $items_per_page);

        // Set up the pagination
        $this->set_pagination_args(array(
            'total_items' => $total_items,
            'per_page'    => $items_per_page,
        ));
        $this->_column_headers = array($columns, $hidden, $sortable);
        $this->items = $data;
    }

    public function get_columns()
    {
        return array(
            'cb' => '<input type="checkbox" />',
            'order_id' => 'Order ID',
            'order_date' => 'Date',
            'order_status' => 'Order Status',
            'total' => 'Total',
            'user_name'  => 'User Name',
            'review_count'  => 'Review Count',
            'schedule' => 'Schedule',
            'arguments' => 'Arguments',
            'next_execution' => 'Next Execution',

        );
    }
    public function get_bulk_actions()
    {
        $actions = array(
            'send_email' => 'Send Email',
        );
        return $actions;
    }

    protected function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" name="order_id[]" value="%s" />',
            $item['order_id']
        );
    }

    public function get_sortable_columns()
    {
        return array(
            'order_id' => array('id', false),
            'user_name' => array('user_name', false),
            'order_status' => 'Order Status',
            'order_date' => array('order_date', false),
            'total'  => array('total', false),
        );
    }

    public function process_bulk_action()
    {
        echo ('Bulk action triggered: ' . esc_html($this->current_action()));

        if ('send_email' === $this->current_action()) {

            // Verify nonce for bulk action
            if (!isset($_REQUEST['_wpnonce']) || !wp_verify_nonce(sanitize_text_field(wp_unslash($_REQUEST['_wpnonce'])), 'bulk-' . $this->_args['plural'])) {
                wp_die('Security check failed. Please try again.');
            }

            $order_ids = isset($_REQUEST['order_id']) ? array_map('sanitize_text_field', wp_unslash((array)$_REQUEST['order_id'])) : array();
            // echo($order_ids);
            // die();
            foreach ($order_ids as $order_id) {
                // Retrieve the email address for the order
                $order = wc_get_order($order_id);
                $user_id = $order->get_customer_id();
                $user = get_user_by('ID', $user_id);
                $products = array();

                foreach ($order->get_items() as $item_id => $item) {
                    // Get product data
                    $product = $item->get_product();

                    // Add product details to the array
                    $products[] = array(
                        'name' => $product->get_name(),
                        'url' => $product->get_permalink(), // Get product URL
                        'image' => get_the_post_thumbnail_url($product->get_id(), 'full'),
                        'delivery_date' => gmdate('Y-m-d', strtotime($order->get_date_created())),
                    );
                }



                $this->send_review_reminder_email($user->user_email, $user->user_login, $products);
            }
        }
    }

    public function check_and_send_review_emails()
    {
        $title = 'Test user'; // send here from contact form
        $email = '<EMAIL>'; // send here from contact form
        //init
        $message = 'ddddddddddd';
        $to = '<EMAIL>';
        $subject = 'My Contact Form - ' . $title;
        //set headers
        $headers[] = 'From: ' . get_bloginfo('name') . ' <' . $to . '>';
        $headers[] = 'Reply-To: ' . $title . ' <' . $email . '>';
        $headers[] = 'Content-Type: text/html: charset=UTF-8';

        //send email
        $sent_message = wp_mail($to, $subject, $message, $headers);

        if ($sent_message) {
            echo 'The test message was sent. Check your email inbox.';
        } else {
            echo 'The message was not sent!';
        }
    }


    private function send_review_reminder_email($to, $user_name, $products)
    {
        $title = 'Test user'; // Customize as needed
        $email = '<EMAIL>'; // Customize as needed
        $site_name = get_bloginfo('name'); // Get the site name

        // Get the site logo URL
        $custom_logo_id = get_theme_mod('custom_logo');
        $logo_info = wp_get_attachment_image_src($custom_logo_id, 'full');
        $logo_url = $logo_info[0];



        // HTML email content with header, body, and footer
        $message = '
            <html>
            <head>
                <title>Review Reminder</title>
                <style>
                    /* Add your CSS styles here */
                    body {
                        font-family: Arial, sans-serif;
                        background-color: #f4f4f4;
                        color: #333;
                    }
                    .email-container {
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #fff;
                        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    }
                    .header {
                        background-color: #ffa500;
                        color: #fff;
                        padding: 5px;
                        text-align: center;
                    }
                    .header img {
                        max-width: 100%;
                        height: auto;
                    }
                    .footer {
                        background-color: #f4f4f4;
                        padding: 10px;
                        text-align: center;
                    }
                    .content {
                        padding: 20px;
                    }
                    .content p {
                        color: #1a2e44;
                    }
                    .footer a {
                        color: #1a2e44;
                    }
                    .footer p {
                        font-size: 14px;
                    }
                    .product {
                        display: flex;
                        align-items: center;
                        border: 1px solid #ddd;
                        padding: 10px;
                        margin-bottom: 10px;
                    }
                    .product img {
                        max-width: 80px;
                        margin-right: 10px;
                    }
                    .product-info {
                        flex-grow: 2;
                    }

                /* CSS */
                .product .btn {
                align-items: center;
                background-clip: padding-box;
                background-color: #fa6400;
                border: 1px solid transparent;
                border-radius: .25rem;
                box-shadow: rgba(0, 0, 0, 0.02) 0 1px 3px 0;
                box-sizing: border-box;
                color: #fff;
                cursor: pointer;
                display: inline-flex;
                font-family: system-ui,-apple-system,system-ui,"Helvetica Neue",Helvetica,Arial,sans-serif;
                font-size: 15px;
                font-weight: 600;
                justify-content: center;
                line-height: 1.25;
                margin: 0;
                min-height: 2.5rem;
                padding: calc(.875rem - 1px) calc(1.5rem - 1px);
                position: relative;
                text-decoration: none;
                transition: all 250ms;
                user-select: none;
                -webkit-user-select: none;
                touch-action: manipulation;
                vertical-align: baseline;
                width: auto;
                margin-top:10px;
                }

                .product .btn:hover,
                .product .btn:focus {
                background-color: #fb8332;
                box-shadow: rgba(0, 0, 0, 0.1) 0 4px 12px;
                }

                .product .btn:hover {
                transform: translateY(-1px);
                }

                .product .btn:active {
                background-color: #c85000;
                box-shadow: rgba(0, 0, 0, .06) 0 2px 4px;
                transform: translateY(0);
                }
                </style>
            </head>
            <body>
                <div class="email-container">
                    <div class="header">
                        <h1>Review Reminder</h1>
                        ' . wp_get_attachment_image($custom_logo_id, 'full', false, array('alt' => $site_name . ' Logo')) . '
                    </div>
                    <div class="content">
                        <h4>Share your product review</h4>
                        <p>Dear ' . $user_name . ',</p>
                        <p>Now that you have received the following items, how about leaving a review?</p>';

        // Add product details to the email
        foreach ($products as $product) {

            $message .= '
                        <div class="product">
                            ' . $this->get_product_image_html($product['image'], $product['name']) . '
                            <div class="product-info">
                                <p><strong>' . $product['name'] . '</strong></p>
                                <p>Delivered on: ' . $product['delivery_date'] . '</p>
                            </div>
                            <div>
                            <a class="ratting_icon" href="' . $product['url'] . '">⭐⭐⭐⭐⭐</a>
                            <br>
                            <a class="btn" href="' . $product['url'] . '">Share My Review</a>
                            </div>

                        </div>';
        }

        $message .= '
                    </div>
                    <div class="footer">
                        <p><a href="#">Privacy Policy</a> | <a href="#">Terms & Conditions</a></p>
                        <p>This is an automatically generated e-mail from our subscription list.<br> Please do not reply to e-mail.</p>
                        <p>For any questions, contact us at ' . $email . '</p>
                    </div>
                </div>
            </body>
            </html>
        ';

        $subject = 'Review Reminder - ' . $title;

        // Set headers for HTML email
        $headers[] = 'From: ' . $site_name . ' <' . $to . '>';
        $headers[] = 'Reply-To: ' . $title . ' <' . $email . '>';
        $headers[] = 'Content-Type: text/html; charset=UTF-8';

        // Send email
        $sent_message = wp_mail($to, $subject, $message, $headers);

        // Log the result
        if ($sent_message) {
            // Log success message to a custom log file instead of error_log
            // Uncomment for debugging: file_put_contents(WP_CONTENT_DIR . '/review-reminder-logs.txt', 'Email sent to ' . $to . ' at ' . gmdate('Y-m-d H:i:s') . "\n", FILE_APPEND);
        } else {
            // Log error message to a custom log file instead of error_log
            // Uncomment for debugging: file_put_contents(WP_CONTENT_DIR . '/review-reminder-logs.txt', 'Failed to send email to ' . $to . ' at ' . gmdate('Y-m-d H:i:s') . "\n", FILE_APPEND);
        }
    }

    private function table_data()
    {
        // Define the arguments for the order query
        $args = array(
            'numberposts' => -1,
            'post_type' => 'shop_order',
            'post_status' => array('wc-delivered'),
        );

        // Get the orders based on the query
        $orders = wc_get_orders($args);
        $data = array();
        foreach ($orders as $order) {



            $user_id = $order->get_customer_id();
            $order_date = gmdate('F j, Y', strtotime($order->get_date_created()));
            $order_status = $order->get_status();
            // $order_view_link = get_edit_post_link($order->get_id());
            $order_total = $order->get_total();

            // Retrieve the product ID associated with the order (modify this based on your WooCommerce setup)
            $items = $order->get_items();
            $product_id = '';

            // Loop through the order items to find the product ID
            foreach ($items as $item) {
                $product_id = $item->get_product_id();
                break;
            }

            // Check if the user has left any comments or reviews on the product
            $args = array(
                'user_id' => $user_id,
                'post_id' => $product_id,
                'status' => 'approve',
                'type' => 'review',
                'count' => true
            );

            // Get the count of user comments/reviews
            $user_comments_count = get_comments($args);

            $user = get_user_by('ID', $user_id);
            $user_name = $user->user_login;
            $user_email = $user->user_email;

            //  // Check if the order has been delivered for 3 days and no review is left
            //  $order_created_timestamp = strtotime($order->get_date_created());
            //  $three_days_ago = strtotime('-3 days');

            //  if ($order_created_timestamp <= $three_days_ago && $user_comments_count === 0) {
            //      // Send email logic (similar to what you had in process_bulk_action)
            //      $this->send_review_reminder_email($user_email, $user_name, $products);
            //  }

            $data[] = array(
                'order_id' => $order->get_id(),
                'user_name' => '<span id="username">' . $user_name . '</span>' . '<br>' . '<span id="user-email">' . $user_email . '</span>',
                'order_date' => $order_date,
                'order_status' => $order_status,
                'total' => $order_total,
                'review_count' => $user_comments_count,
                'schedule' => 'Schedule',
                'arguments' => 'Arguments',
                'next_execution' => 'Next Execution',

            );
        }

        return $data;
    }

    public function column_default($item, $column_name)
    {

        return $item[$column_name];
    }

    public function column_order_status($item)
    {
        return $item['order_status'];
    }

    public function column_order_date($item)
    {
        return $item['order_date'];
    }

    /**
     * Helper method to get product image HTML using WordPress functions
     *
     * @param string $image_url The image URL
     * @param string $product_name The product name for alt text
     * @return string HTML for the product image
     */
    private function get_product_image_html($image_url, $product_name)
    {
        // Try to get attachment ID from URL
        $attachment_id = attachment_url_to_postid($image_url);

        if ($attachment_id) {
            // If we have an attachment ID, use wp_get_attachment_image
            return wp_get_attachment_image($attachment_id, 'thumbnail', false, array(
                'alt' => esc_attr($product_name),
                'class' => 'product-image'
            ));
        } else {
            // Fallback to WooCommerce placeholder if no attachment ID found
            return wc_placeholder_img('thumbnail', array('alt' => esc_attr($product_name), 'class' => 'product-image'));
        }
    }
}




function revinefo_email_reminder_table_plugin_page()
{
    $output = '<div class="wrap"><h2>No Review Order list</h2>';
    $output .= '<form method="post" action="">';
    $output .= wp_nonce_field('bulk-email-reminders', '_wpnonce', true, false);

    $custom_table = new REVINEFO_Email_Reminder_Table_Plugin();
    $custom_table->search_box('Search Orders', 'order_id');
    $custom_table->prepare_items();
    ob_start();
    $custom_table->display();
    $output .= ob_get_clean();
    $output .= '</form>';
    $output .= '</div>';

    echo wp_kses_post($output);
}



revinefo_email_reminder_table_plugin_page();
