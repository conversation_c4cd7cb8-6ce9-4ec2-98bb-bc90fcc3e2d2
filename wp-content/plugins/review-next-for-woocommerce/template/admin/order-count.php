<?php
// Exit if accessed directly
if (! defined('ABSPATH')) exit;

if (!class_exists('WP_List_Table')) {
    require_once(ABSPATH . 'wp-admin/includes/class-wp-list-table.php');
}

class REVINEFO_Order_Count_Table_Plugin extends WP_List_Table
{
    public function __construct()
    {
        parent::__construct(array(
            'singular' => 'order-count',
            'plural'   => 'order-counts',
            'ajax'     => true,
        ));
    }

    public function prepare_items()
    {
        // Define the columns and data
        $columns = $this->get_columns();
        $hidden = array();
        $sortable = $this->get_sortable_columns();
        $data = $this->table_data();
        // Verify nonce for form submission
        if (isset($_POST['_wpnonce']) && wp_verify_nonce(sanitize_text_field(wp_unslash($_POST['_wpnonce'])), 'bulk-' . $this->_args['plural'])) {
            // Get the search query from the form submission and sanitize it
            $search_query = isset($_POST['s']) ? sanitize_text_field(wp_unslash($_POST['s'])) : '';
        } else {
            $search_query = '';
        }

        // If a search query is provided, filter the data
        if (!empty($search_query)) {
            $data = array_filter($this->table_data(), function ($item) use ($search_query) {
                return stripos($item['order_id'], $search_query) !== false;
            });
        } else {
            $data = $this->table_data();
        }
        // Get the current page number and items per page from the URL
        $current_page = $this->get_pagenum();
        $items_per_page = 10; // You can adjust the number of items per page

        // Calculate the total number of items
        $total_items = count($this->table_data());

        // Create a paginated data array based on the current page
        $data = array_slice($this->table_data(), (($current_page - 1) * $items_per_page), $items_per_page);

        // Set up the pagination
        $this->set_pagination_args(array(
            'total_items' => $total_items,
            'per_page'    => $items_per_page,
        ));
        $this->_column_headers = array($columns, $hidden, $sortable);
        $this->items = $data;
    }

    public function get_columns()
    {
        return array(
            'cb' => '<input type="checkbox" />',
            'order_id' => 'Order ID',
            'order_date' => 'Date',
            'order_status' => 'Order Status',
            'total' => 'Total',
            'user'  => 'User',
            'order_count'  => 'Order Count',
            'view'  => 'View',
        );
    }


    protected function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" name="order_id[]" value="%s" />',
            $item['order_id']
        );
    }

    public function get_sortable_columns()
    {
        return array(
            'order_id' => array('id', false),
            'user' => array('user', false),
            'order_status' => 'Order Status',
            'order_date' => array('order_date', false),
            'total'  => array('total', false),
        );
    }

    public function process_bulk_action() {}


    private function table_data()
    {
        // Check if WooCommerce is active and function exists
        if (!function_exists('wc_get_orders') || !class_exists('WooCommerce')) {
            return array(); // Return empty array if WooCommerce is not available
        }

        // Define the arguments for the order query
        $args = array(
            'numberposts' => -1,
            'post_type' => 'shop_order',
            'post_status' => array('wc-delivered'),
        );

        // Get the orders based on the query
        $orders = wc_get_orders($args);
        $data = array();

        foreach ($orders as $order) {
            $user_id = $order->get_customer_id();
            $user = get_user_by('ID', $user_id);

            // Check if user exists and has required data
            if (!$user || !isset($user->user_login) || !isset($user->user_email)) {
                continue; // Skip this order if user data is invalid
            }

            $user_name = $user->user_login;
            $user_email = $user->user_email;

            $data[] = array(
                'order_id' => $order->get_id(),
                'user' => '<span id="username">' . esc_html($user_name) . '</span>' . '<br>' . '<span id="user-email">' . esc_html($user_email) . '</span>',
                'order_date' => gmdate('F j, Y', strtotime($order->get_date_created())),
                'order_status' => $order->get_status(),
                'total' => $order->get_total(),
                'order_count' => '',
                'view' => 'dd',
            );
        }

        return $data;
    }


    public function column_default($item, $column_name)
    {
        switch ($column_name) {
            case 'view':
                return '<a href="' . wp_nonce_url('?page=order-details&order_id=' . $item['order_id'], 'view_order_details') . '">View Details</a>';
            default:
                return $item[$column_name];
        }
    }
}




function revinefo_order_count_table_plugin_page()
{
    $output = '<div class="wrap"><h2>Order Count list</h2>';

    // Check if WooCommerce is active
    if (!function_exists('wc_get_orders') || !class_exists('WooCommerce')) {
        $output .= '<div class="notice notice-warning"><p>';
        $output .= '<strong>WooCommerce Required:</strong> This feature requires WooCommerce to be installed and activated.';
        $output .= '</p></div>';
        $output .= '</div>';
        echo wp_kses_post($output);
        return;
    }

    $output .= '<form method="post" action="">';
    $output .= wp_nonce_field('bulk-order-counts', '_wpnonce', true, false);

    $custom_table = new REVINEFO_Order_Count_Table_Plugin();
    $custom_table->search_box('Search Orders', 'order_id');
    $custom_table->prepare_items();
    ob_start();
    $custom_table->display();
    $output .= ob_get_clean();
    $output .= '</form>';
    $output .= '</div>';

    echo wp_kses_post($output);
}




revinefo_order_count_table_plugin_page();
